import json

import pandas as pd

from util.common_util import is_empty
from core.processor import normalize_item_name


def normalize_newlines_and_replace(s):
    # 将连续出现的 \n 替换成一个 \n
    while '\n\n' in s:
        s = s.replace('\n\n', '\n')

    # 将 \n 替换成空格
    s = s.replace('\n', ' ')

    return s


# 指定 Excel 文件的路径
file_path = "/Users/<USER>/workspace/inference/config/SPU信息.xlsx"

# 读取 Excel 文件中的所有 sheet
xls = pd.ExcelFile(file_path)

# 获取所有 sheet 的名称
sheet_names = xls.sheet_names

# 创建一个字典来存储每个 sheet 的数据
sheet_data = {}

item_name_param_config_dict = dict()

# 读取每个 sheet 的数据并存储在字典中
for sheet_name in sheet_names:
    data = pd.read_excel(xls, sheet_name=sheet_name, header=None,
                         names=['first_category', 'second_category', 'content'], dtype=str)
    first_category_content_dict = dict()
    for _, row in data.iterrows():
        first_category, second_category, content = row["first_category"], row["second_category"], row["content"]
        # print(first_category, second_category, content)
        if first_category == "Produk":
            # skip first line with Produk
            continue

        if pd.isna(content):
            continue

        if content.startswith("*"):
            # skip comment
            continue

        content = normalize_newlines_and_replace(content.strip())
        if is_empty(content):
            continue

        if first_category not in first_category_content_dict:
            first_category_content_dict[first_category] = list()
        if pd.notna(second_category) and len(second_category) > 1:
            added = second_category + " " + content
        else:
            added = content
        first_category_content_dict[first_category].append(added)
    final_content_list = list()
    for first_category in first_category_content_dict:
        merged = ' '.join(first_category_content_dict[first_category])
        final_content_list.append(f"{first_category}: {merged}")
    normalized_name = normalize_item_name(sheet_name)
    item_name_param_config_dict[normalized_name] = final_content_list

with open("../config/minet_param.json", "w") as fout:
    # fout.write(json.dumps(item_name_param_config_dict, indent=2, ensure_ascii=False))
    json.dump(item_name_param_config_dict, fout)
