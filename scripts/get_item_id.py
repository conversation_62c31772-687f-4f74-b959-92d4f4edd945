import requests
import json

# URL
URL = 'https://one.mi.com/api/miapi/ApiTest/dubboTest'

# Headers
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://one.mi.com',
    'priority': 'u=1, i',
    'referer': 'https://one.mi.com/miapi/',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
}

# Cookies
COOKIES = {
    'xmuuid': 'XMGUEST-09A59880-0A0F-11F0-AD8C-0F965A5158B6',
    'xmUuid': 'XMGUEST-09A59880-0A0F-11F0-AD8C-0F965A5158B6',
    'mstuid': '1742972015265_3432',
    'Xm_User_Language': '',
    'canary': '1',
    'notice_behavior': 'implied,us',
    'HMACCOUNT': '88402945F1D5D285',
    'mishopDeviceId': 'BJ7JR5ccOE4guQn2QjdNAtUEpx2WRgNeFo9H5TIWF5I7Cra0LWbujJLwTrvIN4LV/3bydL+jX6mSoAfAmd74/aQ==',
    'deviceId': 'xmdevice_w17ybzeixyg6yv1m',
    '_cas_session': 'rRNfmQwnNwdRNoDb9JyPNwmp2eEMkLEgczvnszL9nmJjfeM4a4yclBMPYBoCLxul',
    '_utm_data': '{"mtm":"Thirdparty.Baidu.ProductUnion.BrandZone-Baidu-PC.Xiaomi-H-224","device_id":""}',
    '_fbp': 'fb.1.*************.****************',
    'notice_preferences': '3:',
    'TAconsentID': '8ef6c03f-437e-4cb6-9956-10cd22ed1eca',
    'notice_gdpr_prefs': '0,1,2,3:',
    'cmapi_gtm_bl': '',
    'cmapi_cookie_privacy': 'permit 1,2,3,4',
    '_gcl_au': '1.1.**********.**********',
    '_ga': 'GA1.1.**********.**********',
    '_tt_enable_cookie': '1',
    '_ttp': '01JZ0CCG11NREJ3AA7K9QNDWZB_.tt.1',
    '_uetvid': 'c1704b80534911f0ac090f992f419a11',
    'ttcsid_CP5VVV3C77U9IUEM2GHG': '*************::JRoPGkvkOa53NSBv7dOV.1.********81691',
    'ttcsid': '*************::3es5GyO0AMV_YUlbZn6R.1.********81691',
    '_ga_M7BZ346GN7': 'GS2.1.s1751333211$o2$g0$t1751333211$j60$l0$h0',
    '_ga_M8KNCVNGBK': 'GS2.1.s1751333227$o2$g0$t1751333227$j60$l0$h0',
    '_ga_RJV5MZ74WC': 'GS2.1.s1751333228$o2$g0$t1751333228$j60$l0$h0',
    '_ga_TN8XYTFEY2': 'GS2.1.s1751333228$o2$g0$t1751333228$j60$l0$h0',
    'lastsource': 'www.google.com.hk',
    'Hm_lvt_c3e3e8b3ea48955284516b186acf0f4e': '1749001381,1751343424',
    'Hm_lpvt_c3e3e8b3ea48955284516b186acf0f4e': '1751545547',
    'xm_vistor': '1742972015265_3432_1751545547239-1751545547239',
    'mstz': '||1296917624.80||https%253A%252F%252Fwww.google.com.hk%252F|https%25253A%25252F%25252Fwww.google.com.hk%25252F',
    'pageid': '1eeac1144fb794ae',
    '_aegis_cas': 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJuYmYiOjE3NTI2NTAyMzgsImRlcGlkIjoiK1x1MDAxYkxoMlx1MDAwMXpgaV1WY0NcdTAwMTlcXHJnXHUwMDA0aV1UY0AiLCJhdWQiOiJvbmUubWkuY29tIiwiYyI6MCwiZGV0YWlsIjoi6PlM11x1MDAwMHgzt4vlXHUwMDFjPyd9qa05vlhcdTAwMDB2-CxcdTAwMTZ6ont8Unxdz_7_eqqDioTnyk23weg5e3buXHUwMDBlJu2ArGX-RIRcdFx1MDAxOYCs-fxcdTAwMTFkUYUu7lxmeISC0dbEgIhDivanlzS8_Fx1MDAxNFx1MDAwNjmLMOxJx1x1MDAxZDV94FxyXHUwMDEw0mZ-qmao6IpcdTAwMDVcdTAwMTnsvFx1MDAxNfZcL5OsrF5a-rmZjNH7hKsmsePpXHUwMDEzI_R7XHUwMDEx0czhRXiTz2TEeVx1MDAxOLSIvCY65zBSaV9uojp371x1MDAwZnPflF-HpPiwdmdcdTAwMWMh61x1MDAxOVlFXHUwMDE1XHUwMDA105EqTtlcdTAwMTdcdTAwMDR6oCp2IVx1MDAxNEVcL3nrkyo3XHUwMDBmSadcdTAwMDb6JUpCXHUwMDFkm8dOu1x1MDAwMi69XHUwMDE00FJnlVx1MDAwZk8zam_UrkPEXHUwMDEy8o7XMTT5d8HFvLwmJKN3xL7zZVA6z9kk_D3FZkNZi6b5sLarrXpcdTAwMDPHPPrIzLM_xPMxM4lcYoPjLMlC6aqJy1x1MDAxMItcdTAwMWX-eoZcdTAwMGZiT7zKjlZcdTAwMTVcdTAwMGVEXHUwMDAwmHVaoDfbd1x1MDAxM96d3UzEXu1cdTAwMTPTtmlDlFx1MDAxNLVcdTAwMGUxdWOON2qp4JZgp3rgreRDXHUwMDBlXHUwMDA3irm1X1AmsvPUQrtehC2Efc7JQeN19bFcIuLLXHUwMDE4S_ZcdTAwMWGYprNEjq1is_P1Xcpt9z_IqG1cdTAwMTdcdTAwMDV8XGZcdTAwMTTv53xPvrZcdTAwMTU3zbb-oT0xppnyXC98dq_yOkQluCdbj42SWCZUWtVPUN5cdTAwMWS3wEhX-fmxK8LZPjPUr_Tnd-qp1I14yuJcdTAwMTDqmMOdXHUwMDAw9adfRJbl1TrFbOtHPpJcdTAwMTM4XHUwMDAzYjpcdTAwMDW01Vx1MDAxM5PT3jFZbtq8PFx1MDAxZUhor328zfLyOeg4TF1cdTAwMTaQZZKnymi8w9rB58aiYklidtlcL5TSymgy01wvXHUwMDBiIzQ6XHUwMDAzWrj_hNBZa-5Etlx1MDAwMMOHb49dN99cdTAwMTNcdTAwN2aggFx1MDAxZlpTbVxu-55nK3D1TUbEXHUwMDEys_ZcdTAwMGLtPp2KM2yiO83czotcdTAwMWatfKVDmSNy997OX09SvPxcLyRL0dMrbEAq7Fx1MDA3ZnrVXHUwMDdmnFxuXHUwMDA3nJAqq-yM8zOs6pNZrKc8dXydPec8z6q3s-Bjglx1MDAwYjsxXFzrOaZcdTAwMDX0rcdCfDhcdTAwMWP3a4DjgiIsInN1YiI6Imhhbm1pbmczIiwidCI6ImZhbHNlIiwidXQiOiJcdTAwMDM_XHUwMDA2TVx0QlZRIiwiZXhwIjoxNzUyNzQwMjM4LCJkIjoiNjdkY2M0NmM0YzRkNDhhZjhmNGRiYzkzMmZkZDc0MzMiLCJpc3MiOiJNSS1JTkZPU0VDIiwibCI6IiVcdTAwMWE4XHUwMDExVlxuIiwidHlwIjoiY2FzIn0.LcHmrKFnUDoS-uC86BOrrrssiFjMihtDf-A-BkhYHpD6Hsf1S-gQSLKJ7SlpKo0moqHHPh9QQkDyEVkk5AH45g'
}


def get_item_id(item_name):
    # 发送请求
    # Data
    parameter = [
        {"business": "i18n", "itemName": item_name, "areaId": "ID", "page": {"total": 0, "pageSize": 10, "pageNum": 1}}]

    data = {
        'interfaceName': 'com.xiaomi.nr.goods.api.ItemService',
        'methodName': 'pageItem',
        'group': 'sg_online',
        'version': '1.0',
        'production': 'true',
        'retries': '1',
        'paramType': '["com.xiaomi.nr.goods.api.request.item.PageItemRequest"]',
        'parameter': json.dumps(parameter),
        'timeout': '5000',
        'genParam': 'false',
        'env': 'singapore'
    }
    response = requests.post(URL, headers=HEADERS, cookies=COOKIES, data=data)

    # 如果响应是 JSON 格式，可以这样解析
    try:
        json_response = response.json()
        result = json.loads(json_response["data"]["res"])
        for sub_result in result["data"]["list"]:
            if sub_result["itemName"] == item_name:
                return sub_result["itemId"]
    except json.JSONDecodeError:
        print("Response is not in JSON format")
    return None


# 获取印尼发布的所有商品列表
def get_all_items():
    expected_page_size = 10
    page_num = 0
    actual_page_size = expected_page_size
    item_name_id_dict = dict()
    while True:
        if actual_page_size < expected_page_size:
            break

        parameter = [
            {"business": "i18n", "areaId": "ID", "page": {"pageSize": expected_page_size, "pageNum": page_num}}]
        page_num += 1
        data = {
            'interfaceName': 'com.xiaomi.nr.goods.api.ItemService',
            'methodName': 'pageItem',
            'group': 'sg_online',
            'version': '1.0',
            'production': 'true',
            'retries': '1',
            'paramType': '["com.xiaomi.nr.goods.api.request.item.PageItemRequest"]',
            'parameter': json.dumps(parameter),
            'timeout': '5000',
            'genParam': 'false',
            'env': 'singapore'
        }
        response = requests.post(URL, headers=HEADERS, cookies=COOKIES, data=data)
        # 如果响应是 JSON 格式，可以这样解析
        try:
            json_response = response.json()
            result = json.loads(json_response["data"]["res"])
            actual_page_size = len(result["data"]["list"])
            print(f"page_num={page_num}, actual_page_size={actual_page_size}")
            for sub_result in result["data"]["list"]:
                if sub_result["itemName"] in item_name_id_dict:
                    old_id = item_name_id_dict[sub_result["itemName"]]
                    if old_id == sub_result["itemId"]:
                        continue

                    print(f"查询到了重复的商品（但他们的 id 不一样）：{sub_result["itemName"]}（{old_id}->{ sub_result["itemId"]}）")
                    continue

                item_name_id_dict[sub_result["itemName"]] = sub_result["itemId"]
        except json.JSONDecodeError:
            print("Response is not in JSON format")
    return item_name_id_dict

if __name__ == '__main__':
    item_name_id_dict = get_all_items()
    with open("/Users/<USER>/workspace/inference/config/item_id_name_ind_2025-07-17.json", "w") as fout:
        fout.write(json.dumps(item_name_id_dict, indent=2, ensure_ascii=False))
