import json
import os

from fds import GalaxyFDSClient, FDSClientConfiguration

CLIENT = GalaxyFDSClient(
    access_key="AKKKWPJ7BIKNJ794AE",
    access_secret="t6FGmBLF1atPhRqnPb7Ke4APjqa8ZfGlv1atJbUP",
    config=FDSClientConfiguration(
        endpoint="alsgp0-fds.api.xiaomi.net",
        enable_cdn_for_upload=False,
        enable_cdn_for_download=False,
    ),
)

if __name__ == '__main__':
    html_dir = "/Users/<USER>/workspace/inference/tmp/html"
    bucket_name = "copilot-faq"
    # 不要在页面上创建 bucket，否则上传的时候会报错：Access Denied，需要用下面的方式创建 bucket
    # CLIENT.create_bucket(bucket_name)
    # exit()
    file_name_list = os.listdir(html_dir)
    item_name_url_dict = dict()
    idx = 0
    for file_name in file_name_list:
        file_path = os.path.join(html_dir, file_name)
        # upload
        with open(file_path) as fin:
            content = fin.read()
            CLIENT.put_object(bucket_name, file_name, fin)
        # get url
        url = CLIENT.generate_download_object_uri(bucket_name, file_name)
        url = url.replace("https://alsgp0-fds.api.xiaomi.net/", "https://alsgp0.fds.api.xiaomi.com/")
        item_name = file_name.split(".")[0]
        item_name_url_dict[item_name] = url
        print(f"{idx} {file_name} {url}")
        idx += 1
    with open("../config/item_name_to_faq_url.json", "w") as fout:
        fout.write(json.dumps(item_name_url_dict, indent=2, ensure_ascii=False))
