import json
import os

from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST, DATA_BASE_USER, DATA_BASE_PASSWORD, DATA_BASE_NAME, \
    DATA_BASE_PORT
from core.enum.content_type import ContentType
from core.enum.doc_type import DocType
from core.schema.doc_trace_source import DocTraceSource
from util.common_util import get_env_by_key, render_page, merge_faq_sources
from core.schema.constant import TEST_ENV, ENV_NAME_KEY
from util.file_util import mkdir_if_not_exists
from util.mysql_db_manager import DBManager

ENV_NAME = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
DB = DBManager(
    host=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_HOST],
    user=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_USER],
    password=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PASSWORD],
    database=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_NAME],
    port=int(RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PORT]),
    batch_size=1000
)


def merge_faq_by_item(item_name, save_dir):
    # 聚合每个商品的 faq
    # ToDo(hm): 统一放到 db manager 中并合并下
    query = f"""
                SELECT title, content_list
                FROM doc_trace_source
                WHERE item_name = %s
                AND doc_type = 1 
                AND is_deleted = 2
            """
    result_list = DB.query_data(query, (item_name,))
    doc_trace_source_list = list()
    for result in result_list:
        question = result["title"]
        content_list = json.loads(result["content_list"])
        html_page = render_page(question, content_list)
        doc_trace_source = DocTraceSource(item_id="",
                                          item_name=item_name,
                                          doc_type=DocType.FAQ,
                                          title=question,
                                          content_type=ContentType.HTML,
                                          content=html_page,
                                          raw_content=result["content_list"])
        doc_trace_source_list.append(doc_trace_source)
    merged = merge_faq_sources(doc_trace_source_list)
    mkdir_if_not_exists(save_dir)
    save_path = os.path.join(save_dir, f"{item_name}.html")
    with open(save_path, "w") as fout:
        fout.write(merged.content)


if __name__ == '__main__':
    # 聚合 mysql 中的 faq，输出 html（之后上传 fds）
    html_dir = "/Users/<USER>/workspace/inference/tmp/html"
    item_name_list = DB.query_all_item_name_with_faq()
    idx = 0
    for item_name in item_name_list:
        merge_faq_by_item(item_name, html_dir)
        print(f"{idx} {item_name} faq 合并完成")
        idx += 1
