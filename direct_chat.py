#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to directly call the ChatService.chat method without starting the Uvicorn server.
This allows for testing and debugging the chat functionality directly.
"""

import asyncio
import json
import sys
import traceback
import uuid
from pathlib import Path

from core.enum.language import Language
from core.schema.constant import ENV_NAME_KEY, TEST_ENV
from core.validator import ChatRequestValidator
from core.processor import preprocess_request
from service.chat_service_base import ChatServiceBase

# Get the absolute path of the script file
script_path = Path(__file__).resolve()
# Get the project root directory
project_root = script_path.parent
# Add the project root to the Python path
sys.path.append(str(project_root))

# Change the current working directory to the project root
import os

os.chdir(project_root)
print(f"Working directory set to: {os.getcwd()}")

from loguru import logger

from config.common_config import get_chat_service_init_param, get_chat_service_list, get_db_manager
from core.schema.chat_request import ChatRequest, get_simple_chat_request_v1
from core.schema.chat_response import ChatResponse
from core.enum.event_type import EventType
from util.common_util import decode_sse, pprint, is_empty, get_env_by_key


class MockRequest:
    """Mock FastAPI Request object for testing purposes."""

    def __init__(self):
        self.headers = {"X-Request-ID": f"mock-{uuid.uuid4()}"}
        self.state = type('obj', (object,), {'logger': logger})

    @staticmethod
    async def is_disconnected():
        return False


async def process_chat_response(chat_service: ChatServiceBase, chat_request: ChatRequest) -> ChatResponse:
    ChatRequestValidator().validate_chat_request(chat_request)
    preprocess_request(chat_request)
    chat_request.http_request = MockRequest()
    final_response = None
    try:
        async for chunk in chat_service.chat(chat_request):
            response_dict = decode_sse(chunk)
            if is_empty(response_dict) or "event" not in response_dict:
                continue

            response = ChatResponse.from_dict(response_dict)
            if response.event == EventType.FINISH_EVENT:
                print(f"chat response")
                raw_dict = response.to_dict()
                raw_dict["data"]["answer_type"] = response.data.answer_type.description
                pprint(raw_dict)
                final_response = response
    except Exception as e:
        logger.error(f"Error in chat: {str(e)}: {traceback.format_exc()}")
    return final_response


DB_MANAGER = get_db_manager()
ENV_NAME = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
init_param = get_chat_service_init_param(ENV_NAME, DB_MANAGER)
CHAT_SERVICE_LIST = get_chat_service_list(init_param, DB_MANAGER)


async def direct_chat_to_copilot_async(chat_request) -> ChatResponse:
    context_logger = logger.bind(request_id="request_id", chat_request_id=chat_request.request_id)
    chat_request.logger = context_logger
    ChatRequestValidator().validate_chat_request(chat_request)
    if chat_request.version is None or chat_request.version == 0:
        chat_service = CHAT_SERVICE_LIST[0]
    else:
        chat_service = CHAT_SERVICE_LIST[1]
    final_response = await process_chat_response(chat_service, chat_request)
    return final_response


def direct_chat_to_copilot_from_file(path):
    with open(path, "r") as f:
        data = json.load(f)
    chat_request = ChatRequest.from_dict(data)
    chat_request.request_id = f"direct-{uuid.uuid4()}"
    chat_request.debug = True
    result = asyncio.run(direct_chat_to_copilot_async(chat_request))
    pprint(result)
    return result


def direct_chat_to_copilot(query, item_name=None, language=Language.INDONESIAN):
    request_id = f"direct-{uuid.uuid4()}"
    print(f"request_id={request_id}")
    chat_request = get_simple_chat_request_v1(query, item_name, request_id, language)
    result = asyncio.run(direct_chat_to_copilot_async(chat_request))
    pprint(result)
    return result
