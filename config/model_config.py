from enum import auto, StrEnum

from core.schema.constant import TEST_ENV, PREVIEW_ENV, PROD_ENV

MODEL_VERSION = "v0.0.8"


class ModelConfigKey(StrEnum):
    API_KEY_TEXT = auto()
    API_KEY_JSON = auto()
    API_KEY_JSON_MIFY2 = auto()
    API_KEY_JSON_MIFY3 = auto()
    DATASET_KEY = auto()
    ITEM_ID_NAME_PATH = auto()
    ITEM_NAME_DATASET_ID_PATH = auto()
    ITEM_NAME_COMPETITOR_MAP_PATH = auto()
    MINET_INTRO_PATH = auto()
    MINET_PARAM_PATH = auto()
    API_KEY_TRANSLATE = auto()
    FAQ_URL_PATH = auto()


# 环境 - 配置
# 不保证不同版本的模型配置可兼容
# 这里可以只放「外部」的信息，内部的配置在代码中写死即可
# （其实外部的也可以放到各个地方，只不过根据环境不同，配置也会不同，放在这里统一管理）
MODEL_CONFIG = {
    TEST_ENV: {
        ModelConfigKey.API_KEY_TEXT: "app-i3y8JTgXhIrU4QPKAasdwVal",
        ModelConfigKey.API_KEY_JSON: "app-nVhf8kgf9FwA7fS6hDUkCJZ7",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-pyMZ9rscnYtlmQbl72LskqXl",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-9ArZ4UMtsAOkGZj9BurTQ977",
        ModelConfigKey.DATASET_KEY: "dataset-TsPXNbDYMXJw4a8iQng4ACch",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_test.json",
        ModelConfigKey.ITEM_NAME_COMPETITOR_MAP_PATH: "./config/competitor_map_by_item_name.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.FAQ_URL_PATH: "./config/item_name_to_faq_url.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-EgMaEObyPWEt40se94WTkSBE",
    },
    PREVIEW_ENV: {
        ModelConfigKey.API_KEY_TEXT: "app-VoTbPC92PfI24qssWh3q0haK",
        ModelConfigKey.API_KEY_JSON: "app-B81TzTLAGgBaV31CRgxLaQtM",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-MGky9YwKhdJg3DLHoaXysgaj",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-YKD1GcUgUwspcCpL0ix2TFTM",
        ModelConfigKey.DATASET_KEY: "dataset-jw0XHS9hJ8w6AoSVquSWlunR",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_preview.json",
        ModelConfigKey.ITEM_NAME_COMPETITOR_MAP_PATH: "./config/competitor_map_by_item_name.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.FAQ_URL_PATH: "./config/item_name_to_faq_url.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-FNYKIGUUqRe5qNJCxtE1DIuF",
    },
    PROD_ENV: {
        ModelConfigKey.API_KEY_TEXT: "app-jS4hzQD4aRAipa5xi0MCV0aB",
        ModelConfigKey.API_KEY_JSON: "app-zRfls2ajM5xiD9mayjpKJoNp",
        ModelConfigKey.API_KEY_JSON_MIFY2: "app-rT0QbyTuUt3wtrvkAo8mlqYn",
        ModelConfigKey.API_KEY_JSON_MIFY3: "app-lVdFQuzC63jUihrKgFIsFwCz",
        ModelConfigKey.DATASET_KEY: "dataset-G2EyyAhamqJZJwViVooyKY8v",
        ModelConfigKey.ITEM_ID_NAME_PATH: "./config/item_id_name.json",
        ModelConfigKey.ITEM_NAME_DATASET_ID_PATH: "./config/item_name_dataset_id_prod.json",
        ModelConfigKey.ITEM_NAME_COMPETITOR_MAP_PATH: "./config/competitor_map_by_item_name.json",
        ModelConfigKey.MINET_INTRO_PATH: "./config/minet_intro.json",
        ModelConfigKey.MINET_PARAM_PATH: "./config/minet_param.json",
        ModelConfigKey.FAQ_URL_PATH: "./config/item_name_to_faq_url.json",
        ModelConfigKey.API_KEY_TRANSLATE: "app-FTZNWifEj8ttCUVrhZxPOcES",
    },
}
