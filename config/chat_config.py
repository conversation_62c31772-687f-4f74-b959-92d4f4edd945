from core.enum.message_type import MessageType
from core.enum.language import Language
from core.enum.pre_thinking_message_type import PreThinkingMessageType
from core.schema.constant import QUOTE_SIGN

# 多语言配置放到这里
ENHANCE_QUERY_POSTFIX = {
    Language.CHINESE: "关于",
    Language.ENGLISH: "about ",
    Language.INDONESIAN: "tentang "
}

DETAIL_KEY = {
    Language.CHINESE: "产品详情",
    Language.ENGLISH: "Overview",
    Language.INDONESIAN: "Detail Produk"
}
PARAM_KEY = {
    Language.CHINESE: "产品参数",
    Language.ENGLISH: "Specifications",
    Language.INDONESIAN: "Parameter Produk"
}

REFUSAL_MESSAGE_DICT = {
    MessageType.NOT_IN_SCOPE: {
        Language.CHINESE: "抱歉，我目前只能回答与手机相关（主要是参数配置）的问题。",
        Language.ENGLISH: "Sorry, I can only answer questions related to phones (especially parameter configuration) at this time.",
        Language.INDONESIAN: "<PERSON><PERSON>, saya hanya bisa menjawab pertanyaan terkait ponsel (terutama konfigurasi parameter) saat ini.",
    },
    MessageType.NOT_MATCH_ITEM: {
        Language.CHINESE: "所询问的型号与您选择的不一致，请检查后再询问。",
        Language.ENGLISH: "The model in question does not match your choice, please check before asking.",
        Language.INDONESIAN: "Model dalam pertanyaan tidak sesuai dengan pilihan Anda, silakan periksa sebelum bertanya.",
    },
    MessageType.NO_KNOWLEDGE: {
        Language.CHINESE: "对不起，我没有关于当前问题的知识，无法进行解答。",
        Language.ENGLISH: "Sorry, I have no knowledge of the current issue and cannot answer it.",
        Language.INDONESIAN: "Maaf, saya tidak memiliki pengetahuan tentang masalah saat ini dan tidak dapat menjawabnya.",
    },
    MessageType.DYNAMIC_CHANGE: {
        Language.CHINESE: "产品售价、促销优惠、售后服务等信息请以当地小米官方信息为准。",
        Language.ENGLISH: "Product prices, promotions, after-sales service and other information please refer to local Xiaomi official information.",
        Language.INDONESIAN: "Harga produk, promosi, layanan purna jual, dan informasi lainnya harap merujuk pada informasi resmi Xiaomi setempat.",
    },
    MessageType.NOT_SUPPORTED_ITEM: {
        Language.CHINESE: "抱歉，暂不支持该产品的问答。",
        Language.ENGLISH: "Sorry, this product's Q&A is not supported yet.",
        Language.INDONESIAN: "Maaf, Tanya Jawab produk ini belum didukung.",
    },
    MessageType.ITEM_COMPARE_UNCERTAIN: {
        Language.CHINESE: "对不起，您当前提问的对比机型数量少于2个，无法进行对比。",
        Language.ENGLISH: "Sorry, the number of comparison models you requested is less than 2, so a comparison cannot be performed.",
        Language.INDONESIAN: "Maaf, jumlah model perbandingan yang Anda minta kurang dari 2, jadi perbandingan tidak dapat dilakukan.",
    },
    MessageType.ITEM_UNRECOGNIZED: {
        Language.CHINESE: "对不起，我没有识别到您提问中的机型，请您重新提问。",
        Language.ENGLISH: "Sorry, I don't recognize the model you asked about. Please ask again.",
        Language.INDONESIAN: "Maaf, saya tidak mengenali model yang Anda tanyakan. Silakan bertanya lagi.",
    },
    MessageType.FREE_FAQ_REJECT: {
        Language.CHINESE: "对不起，暂不支持非 3c 数码产品的问答。",
        Language.ENGLISH: "Sorry, we do not yet support inquiries about non-3c digital products.",
        Language.INDONESIAN: "Maaf, kami belum mendukung pertanyaan tentang produk digital non-3c.",
    },
    MessageType.NO_XIAOMI_ITEM: {
        Language.CHINESE: "对不起，暂不支持非小米机型之间的对比。",
        Language.ENGLISH: "Sorry, comparison between non-Xiaomi models is not supported yet.",
        Language.INDONESIAN: "Maaf, perbandingan antara model non-Xiaomi belum didukung.",
    },
    MessageType.ITEM_COMPARE: {
        Language.CHINESE: "请跳转到两款设备对比页面，查看两款产品的区别。",
        Language.ENGLISH: "Please head over to the two devices comparison page to see the differences between the two products.",
        Language.INDONESIAN: "Silakan beralih ke halaman perbandingan dua perangkat untuk melihat perbedaan antara kedua produk.",
    },
    MessageType.ITEM_CANDIDATE: {
        Language.CHINESE: "您想询问以下哪一款产品？",
        Language.ENGLISH: "Which product do you want to ask about?",
        Language.INDONESIAN: "Anda ingin menanyakan produk yang mana?",
    },
    MessageType.CANCELLED: {
        Language.CHINESE: f"{QUOTE_SIGN} 用户已取消请求。",
        Language.ENGLISH: f"{QUOTE_SIGN} User canceled the request.",
        Language.INDONESIAN: f"{QUOTE_SIGN} Pengguna membatalkan permintaan.",
    },
    MessageType.ITEM_COMPARE_FAILED: {
        Language.CHINESE: "双机对比参数数据库查询失败。",
        Language.ENGLISH: "Failed to query the parameter database of the dual machine comparison.",
        Language.INDONESIAN: "Gagal untuk menanyakan basis data parameter perbandingan mesin ganda.",
    },
    MessageType.FILTERED_BY_START_RULE: {
        Language.CHINESE: "您好，请问有什么可以帮到您的？",
        Language.ENGLISH: "Hello, how can I help you?",
        Language.INDONESIAN: "Halo, ada yang bisa saya bantu?"
    }
}

# 其他聊天相关的配置项可以在这里添加
CANDIDATE_ITEM_SIZE = 5  # 候选机型数量 

# 参数问答预置回复语
PRE_MESSAGE_DICT = {
    PreThinkingMessageType.NO_ITEM_NAME: [
        {
            Language.CHINESE: "好的，让我来帮助你。",
            Language.ENGLISH: "Ok, let me help you.",
            Language.INDONESIAN: "Oke, biarkan saya membantu Anda dengan itu."
        },
        {
            Language.CHINESE: "请稍候，我们正在寻找您需要的内容。",
            Language.ENGLISH: "Please wait, we are looking for what you need.",
            Language.INDONESIAN: "Mohon tunggu, kami sedang mencari apa yang Anda butuhkan."
        },
        {
            Language.CHINESE: "好的，我马上帮您查一下相关信息。",
            Language.ENGLISH: "Okay, I will check the relevant information for you right away.",
            Language.INDONESIAN: "Oke, saya akan segera memeriksa informasi yang relevan untuk Anda."
        },
        {
            Language.CHINESE: "我们正在为您检索相关信息。",
            Language.ENGLISH: "Understood. We are retrieving relevant information for you.",
            Language.INDONESIAN: "Dipahami. Kami sedang mengambil informasi yang relevan untuk Anda."
        }
    ],
    PreThinkingMessageType.NO_SECOND_TAG: [
        {Language.CHINESE: "好的，现在我将回答您关于 {} 的问题。",
         Language.ENGLISH: "Okay, now I will answer your questions about {}.",
         Language.INDONESIAN: "Oke, sekarang saya akan menjawab pertanyaan Anda tentang {}."
         },
        {
            Language.CHINESE: "正在检索有关产品 {} 的相关信息。",
            Language.ENGLISH: "Retrieving relevant information about product {}.",
            Language.INDONESIAN: "Mengambil informasi yang relevan tentang produk {}."
        },
        {
            Language.CHINESE: "正在准备产品 {} 的详细信息。",
            Language.ENGLISH: "Preparing detailed information of model {}.",
            Language.INDONESIAN: "Mempersiapkan informasi rinci model {}."
        },
        {
            Language.CHINESE: "好的，我会回答您关于产品 {} 的问题。",
            Language.ENGLISH: "Ok, I will answer your questions about product {}.",
            Language.INDONESIAN: "Oke, saya akan menjawab pertanyaan Anda tentang produk {}."
        }
    ],
    PreThinkingMessageType.TWO_PHONE_COMPARE: [
        {
            Language.CHINESE: "好的，让我来帮您解答关于{}和{}之间的差异，请稍等。",
            Language.ENGLISH: "Okay, let me explain the  differences between {} and {}, Please wait a moment.",
            Language.INDONESIAN: "Baiklah, izinkan saya membantu menjelaskan perbedaan antara {} dan {}. Harap tunggu sebentar.",
        },
        {
            Language.CHINESE: "好的，请允许我为您说明关于{}和{}之间的不同之处，请稍候",
            Language.ENGLISH: "Alright, allow me to clarify the distinctions between {} and {}. Please wait a moment",
            Language.INDONESIAN: "Baiklah, izinkan saya menjelaskan perbedaan antara {} dan {}. Harap tunggu sebentar",
        },
        {
            Language.CHINESE: "我将为您分析{}与{}的异同，请稍待片刻",
            Language.ENGLISH: "Let me tell you the difference between {} and {}. Hang tight.",
            Language.INDONESIAN: "Saya akan menganalisis kesamaan dan perbedaan antara {} dan {}. Mohon tunggu sejenak.",
        },
        {
            Language.CHINESE: "我来给您讲讲{}和{}的区别，您稍等哈。",
            Language.ENGLISH: "Hang tight, I’ll tell you the difference between {} and {}.",
            Language.INDONESIAN: "Saya kasih tahu perbedaan {} dan {} ya. Tunggu sebentar!"
        }
    ],
    PreThinkingMessageType.DEFAULT: [
        {
            Language.CHINESE: "好的，让我来帮您解答关于{}的{}方面的问题。",
            Language.ENGLISH: "Ok, let me help you answer your question about {} aspect of {}.",
            Language.INDONESIAN: "Oke, izinkan saya membantu Anda menjawab pertanyaan Anda tentang aspek {} dari {}.",
        },
        {
            Language.CHINESE: "好的，让我详细解释一下{}的{}方面。",
            Language.ENGLISH: "Okay, let me explain in detail about {} aspect of {}.",
            Language.INDONESIAN: "Oke, biar aku jelaskan secara detail  aspek {} dari {}.",
        },
        {
            Language.CHINESE: "请稍候，我们正在检索有关{}的{}方面的产品信息，供您参考。",
            Language.ENGLISH: "Please wait, we are retrieving product information for your reference about {} aspect of {}.",
            Language.INDONESIAN: "Harap tunggu, kami sedang mengambil informasi produk untuk referensi Anda aspek {} dari {}.",
        },
        {
            Language.CHINESE: "收到，我将为您核实有关{}的{}方面的具体信息。",
            Language.ENGLISH: "Accepted, I will verify specific information for you about {} aspect of {}.",
            Language.INDONESIAN: "Diterima, saya akan memverifikasi informasi spesifik untuk Anda aspek {} dari {}.",
        }
    ]
}

MESSAGE_PROMPT_PREFIX = {
    MessageType.ITEM_CONFIRM: {
        Language.CHINESE: "我想要咨询",
        Language.ENGLISH: "I want to consult",
        Language.INDONESIAN: "Saya ingin berkonsultasi"
    },
    MessageType.ITEM_CANDIDATE: {
        Language.CHINESE: "请确认您想要咨询以下哪一个机型",
        Language.ENGLISH: "Please confirm which of the following models you want to inquire about",
        Language.INDONESIAN: "Harap konfirmasi model mana yang ingin Anda tanyakan berikut"
    }
}

SWAP_ITEM_PREFIX = {
    Language.CHINESE: "接下来我想咨询一下",
    Language.ENGLISH: "Next, I would like to consult",
    Language.INDONESIAN: "Selanjutnya, saya ingin berkonsultasi dengan"
}
