from core.schema.constant import LOCAL_ENV, TEST_ENV, PREVIEW_ENV, PROD_ENV
from util.common_util import get_env_by_key

DOMAIN = "DOMAIN"
API_ACCESS_TOKEN = "API_ACCESS_TOKEN"
DATA_BASE_HOST = "DATA_BASE_HOST"
DATA_BASE_USER = "DATA_BASE_USER"
DATA_BASE_PASSWORD = "DATA_BASE_PASSWORD"
DATA_BASE_NAME = "DATA_BASE_NAME"
DATA_BASE_PORT = "DATA_BASE_PORT"
DATA_TABLE_NAME = "DATA_TABLE_NAME"
PRODUCT_TABLE_NAME = "PRODUCT_TABLE_NAME"
TRACE_SOURCE_TABEL_NAME = "TRACE_SOURCE_TABEL_NAME"
REDIS_HOST = "REDIS_HOST"
REDIS_PORT = "REDIS_PORT"
REDIS_PASSWORD = "REDIS_PASSWORD"
OPENAI_API_KEY = "OPENAI_API_KEY"
MODEL_SOURCE = "MODEL_SOURCE"

MIFY = "mify"
OPENAI = "openai"

RUN_CONFIG_DICT = {
    LOCAL_ENV: {
        DOMAIN: "0.0.0.0:7888",
        API_ACCESS_TOKEN: "Bearer v7d94cxy8p2n6q3kf0m5zs9a1rtg3l7o",
        DATA_BASE_HOST: "gaea.test.mysql01.b2c.srv",  # 中国天津区
        DATA_BASE_USER: "global_salev85_wn",
        DATA_BASE_PASSWORD: "-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl",
        DATA_BASE_NAME: "global_sale_copilot",
        DATA_BASE_PORT: "13306",
        DATA_TABLE_NAME: "global_copilot_question_parse",
        PRODUCT_TABLE_NAME: "global_copilot_product_info",
        TRACE_SOURCE_TABEL_NAME: "doc_trace_source",
        REDIS_HOST: "ares.tj-info-ai-business-efficiency-sale-copilot.cache.srv",
        REDIS_PORT: "22122",
        REDIS_PASSWORD: get_env_by_key(REDIS_PASSWORD),
        OPENAI_API_KEY: get_env_by_key(OPENAI_API_KEY),
        MODEL_SOURCE: get_env_by_key(MODEL_SOURCE, OPENAI)
    },
    TEST_ENV: {
        DOMAIN: "global-copilot.test.cn.srv",
        API_ACCESS_TOKEN: "Bearer v7d94cxy8p2n6q3kf0m5zs9a1rtg3l7o",
        DATA_BASE_HOST: "gaea.test.mysql01.b2c.srv",  # 中国天津区
        DATA_BASE_USER: "global_salev85_wn",
        DATA_BASE_PASSWORD: "-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl",
        DATA_BASE_NAME: "global_sale_copilot",
        DATA_BASE_PORT: "13306",
        DATA_TABLE_NAME: "global_copilot_question_parse",
        PRODUCT_TABLE_NAME: "global_copilot_product_info",
        TRACE_SOURCE_TABEL_NAME: "doc_trace_source",
        REDIS_HOST: "ares.tj-info-ai-business-efficiency-sale-copilot.cache.srv",
        REDIS_PORT: "22122",
        REDIS_PASSWORD: get_env_by_key(REDIS_PASSWORD),
        OPENAI_API_KEY: get_env_by_key(OPENAI_API_KEY),
        MODEL_SOURCE: get_env_by_key(MODEL_SOURCE, OPENAI)
    },
    PREVIEW_ENV: {
        DOMAIN: "global-copilot.pre.sg.srv",
        API_ACCESS_TOKEN: "Bearer 6b2hjk98m4e5x7v3gp1l0o9n2f8c3q2s",
        DATA_BASE_HOST: "cn.ai.international.global-sale-copilot.mysql.srv",  # 中国北京区
        DATA_BASE_USER: "global_salev13_wn",
        DATA_BASE_PASSWORD: "CBBrmkS0-S-56uN84AU4-F_IaCynNCA-",
        DATA_BASE_NAME: "global_sale_copilot",
        DATA_BASE_PORT: "7020",
        DATA_TABLE_NAME: "global_copilot_question_parse",
        PRODUCT_TABLE_NAME: "global_copilot_product_info",
        TRACE_SOURCE_TABEL_NAME: "doc_trace_source",
        REDIS_HOST: "ares.cn-info-ai-business-efficiency-sale-copilot.cache.srv",
        REDIS_PORT: "5123",
        REDIS_PASSWORD: get_env_by_key(REDIS_PASSWORD),
        OPENAI_API_KEY: get_env_by_key(OPENAI_API_KEY),
        MODEL_SOURCE: get_env_by_key(MODEL_SOURCE, OPENAI)
    },
    PROD_ENV: {
        DOMAIN: "global-copilot.prod.sg.srv",
        API_ACCESS_TOKEN: "Bearer 6b2hjk98m4e5x7v3gp1l0o9n2f8c3q2s",
        DATA_BASE_HOST: "sgp.ai.international.global-sale-copilot.mysql.srv",  # 新加坡区
        DATA_BASE_USER: "global_salev67_wn",
        DATA_BASE_PASSWORD: "5IYt1TIbfxKvVa3tuwkE1lERbC66-eSh",
        DATA_BASE_NAME: "global_sale_copilot",
        DATA_BASE_PORT: "6946",
        DATA_TABLE_NAME: "global_copilot_question_parse",
        PRODUCT_TABLE_NAME: "global_copilot_product_info",
        TRACE_SOURCE_TABEL_NAME: "doc_trace_source",
        REDIS_HOST: "ares.sgp-info-ai-business-efficiency-sale-copilot.cache.srv",
        REDIS_PORT: "5102",
        REDIS_PASSWORD: get_env_by_key(REDIS_PASSWORD),
        OPENAI_API_KEY: get_env_by_key(OPENAI_API_KEY),
        MODEL_SOURCE: get_env_by_key(MODEL_SOURCE, OPENAI)
    },
}
