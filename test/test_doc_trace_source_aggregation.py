import unittest
from unittest.mock import Mock

from core.enum.content_type import ContentType
from core.enum.doc_type import DocType
from core.schema.doc_trace_source import DocTraceSource
from service.query_parse_service import QueryParseService


class TestDocTraceSourceAggregation(unittest.TestCase):
    
    def setUp(self):
        # 创建一个 QueryParseService 实例用于测试
        self.service = QueryParseService(
            api_key_json=Mock(),
            api_key_json2=Mock(),
            api_key_json3=Mock(),
            model_manager=Mock(),
            item_name_list=[],
            normalized_item_name_list=[],
            item_name_xiaomi_list=[],
            db=<PERSON>ck(),
            redis_manager=<PERSON><PERSON>()
        )
    
    def test_aggregate_multiple_faqs(self):
        """测试多个 FAQ 聚合为一个"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-1",
                content_type=ContentType.HTML,
                content="<html>FAQ1 content</html>",
                raw_content="FAQ1 raw content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-2",
                content_type=ContentType.HTML,
                content="<html>FAQ2 content</html>",
                raw_content="FAQ2 raw content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数",
                content_type=ContentType.KEY,
                content="test_item",
                raw_content="param content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该有2个结果：1个聚合的FAQ + 1个产品参数
        self.assertEqual(len(result), 2)
        
        # 第一个应该是聚合的FAQ
        faq_result = result[0]
        self.assertEqual(faq_result.doc_type, DocType.FAQ)
        self.assertEqual(faq_result.title, "FAQs")
        self.assertIn("FAQ1 content", faq_result.content)
        self.assertIn("FAQ2 content", faq_result.content)
        self.assertIn("FAQ1 raw content", faq_result.raw_content)
        self.assertIn("FAQ2 raw content", faq_result.raw_content)
        
        # 第二个应该是产品参数
        param_result = result[1]
        self.assertEqual(param_result.doc_type, DocType.PRODUCT_PARAM)
        self.assertEqual(param_result.title, "产品参数")
    
    def test_keep_only_first_product_param(self):
        """测试只保留第一个产品参数"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数1",
                content_type=ContentType.KEY,
                content="param1",
                raw_content="param1 content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数2",
                content_type=ContentType.KEY,
                content="param2",
                raw_content="param2 content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该只有1个结果
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].title, "产品参数1")
        self.assertEqual(result[0].content, "param1")
    
    def test_keep_only_first_product_introduction(self):
        """测试只保留第一个产品详情"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_INTRODUCTION,
                title="产品详情1",
                content_type=ContentType.KEY,
                content="intro1",
                raw_content="intro1 content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_INTRODUCTION,
                title="产品详情2",
                content_type=ContentType.KEY,
                content="intro2",
                raw_content="intro2 content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该只有1个结果
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].title, "产品详情1")
        self.assertEqual(result[0].content, "intro1")
    
    def test_keep_all_sale_tools(self):
        """测试保留所有培训文档"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.SALE_TOOLS,
                title="销售工具1",
                content_type=ContentType.KEY,
                content="tool1",
                raw_content="tool1 content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.SALE_TOOLS,
                title="销售工具2",
                content_type=ContentType.KEY,
                content="tool2",
                raw_content="tool2 content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该有2个结果，都保留
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].title, "销售工具1")
        self.assertEqual(result[1].title, "销售工具2")
    
    def test_maintain_original_order(self):
        """测试保持原始顺序"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_INTRODUCTION,
                title="产品详情",
                content_type=ContentType.KEY,
                content="intro",
                raw_content="intro content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-1",
                content_type=ContentType.HTML,
                content="faq1",
                raw_content="faq1 content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数",
                content_type=ContentType.KEY,
                content="param",
                raw_content="param content"
            ),
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.SALE_TOOLS,
                title="销售工具",
                content_type=ContentType.KEY,
                content="tool",
                raw_content="tool content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该有4个结果，顺序应该保持：产品详情、FAQ、产品参数、销售工具
        self.assertEqual(len(result), 4)
        self.assertEqual(result[0].doc_type, DocType.PRODUCT_INTRODUCTION)
        self.assertEqual(result[1].doc_type, DocType.FAQ)
        self.assertEqual(result[2].doc_type, DocType.PRODUCT_PARAM)
        self.assertEqual(result[3].doc_type, DocType.SALE_TOOLS)
    
    def test_empty_list(self):
        """测试空列表"""
        result = self.service._aggregate_doc_trace_sources([])
        self.assertEqual(len(result), 0)
    
    def test_single_faq_no_aggregation(self):
        """测试单个FAQ不需要聚合"""
        doc_sources = [
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-1",
                content_type=ContentType.HTML,
                content="faq content",
                raw_content="faq raw content"
            )
        ]
        
        result = self.service._aggregate_doc_trace_sources(doc_sources)
        
        # 应该有1个结果，但标题应该改为"FAQs"
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].title, "FAQs")
        self.assertEqual(result[0].content, "faq content")

    def test_complex_mixed_scenario(self):
        """测试复杂的混合场景"""
        doc_sources = [
            # 产品详情1 (应该保留)
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_INTRODUCTION,
                title="产品详情1",
                content_type=ContentType.KEY,
                content="intro1",
                raw_content="intro1 content"
            ),
            # FAQ1
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-1",
                content_type=ContentType.HTML,
                content="faq1 content",
                raw_content="faq1 raw"
            ),
            # 产品参数1 (应该保留)
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数1",
                content_type=ContentType.KEY,
                content="param1",
                raw_content="param1 content"
            ),
            # FAQ2
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-2",
                content_type=ContentType.HTML,
                content="faq2 content",
                raw_content="faq2 raw"
            ),
            # 产品详情2 (应该被过滤掉)
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_INTRODUCTION,
                title="产品详情2",
                content_type=ContentType.KEY,
                content="intro2",
                raw_content="intro2 content"
            ),
            # 销售工具1
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.SALE_TOOLS,
                title="销售工具1",
                content_type=ContentType.KEY,
                content="tool1",
                raw_content="tool1 content"
            ),
            # 产品参数2 (应该被过滤掉)
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.PRODUCT_PARAM,
                title="产品参数2",
                content_type=ContentType.KEY,
                content="param2",
                raw_content="param2 content"
            ),
            # FAQ3
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.FAQ,
                title="FAQ-3",
                content_type=ContentType.HTML,
                content="faq3 content",
                raw_content="faq3 raw"
            ),
            # 销售工具2
            DocTraceSource(
                item_id="item1",
                item_name="test_item",
                doc_type=DocType.SALE_TOOLS,
                title="销售工具2",
                content_type=ContentType.KEY,
                content="tool2",
                raw_content="tool2 content"
            )
        ]

        result = self.service._aggregate_doc_trace_sources(doc_sources)

        # 应该有5个结果：产品详情1、聚合的FAQs、产品参数1、销售工具1、销售工具2
        self.assertEqual(len(result), 5)

        # 验证顺序和内容
        self.assertEqual(result[0].doc_type, DocType.PRODUCT_INTRODUCTION)
        self.assertEqual(result[0].title, "产品详情1")

        self.assertEqual(result[1].doc_type, DocType.FAQ)
        self.assertEqual(result[1].title, "FAQs")
        self.assertIn("faq1 content", result[1].content)
        self.assertIn("faq2 content", result[1].content)
        self.assertIn("faq3 content", result[1].content)
        self.assertIn("faq1 raw", result[1].raw_content)
        self.assertIn("faq2 raw", result[1].raw_content)
        self.assertIn("faq3 raw", result[1].raw_content)

        self.assertEqual(result[2].doc_type, DocType.PRODUCT_PARAM)
        self.assertEqual(result[2].title, "产品参数1")  # 只保留第一个

        self.assertEqual(result[3].doc_type, DocType.SALE_TOOLS)
        self.assertEqual(result[3].title, "销售工具1")

        self.assertEqual(result[4].doc_type, DocType.SALE_TOOLS)
        self.assertEqual(result[4].title, "销售工具2")


if __name__ == '__main__':
    unittest.main()
