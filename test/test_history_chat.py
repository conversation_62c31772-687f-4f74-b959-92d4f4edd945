import unittest

from core.enum.message_type import MessageType
from direct_chat import direct_chat_to_copilot_from_file, direct_chat_to_copilot
from util.common_util import assert_eq, assert_true, is_empty, pprint
from util.compare_util import compare_arrays_ignore_seq
from util.llm_util import assert_false_by_llm
from util.string_util import has_valid_info

LONG_FIRST_TOKEN_ELAPSE_THRESHOLD = 7000
LONG_ANSWER_ELAPSE_THRESHOLD = 20000


def assert_elapse(chat_response, first_token_elapse_threshold=2500, answer_elapse_threshold=7500):
    first_token_elapse = chat_response.data.answer_start_time - chat_response.data.request_receive_time
    print(f"首 token 耗时：{first_token_elapse}")
    assert_true(first_token_elapse < first_token_elapse_threshold)
    answer_elapse = chat_response.data.answer_finish_time - chat_response.data.answer_start_time
    print(f"回答耗时：{answer_elapse}")
    assert_true(answer_elapse < answer_elapse_threshold)


class TestHistoryChat(unittest.TestCase):
    def test0(self):
        path = "test/test_data/530/0_首轮双机对比-清晰的错误SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        # 实际上没有 Xiaomi 15 pro，只识别出 Xiaomi 15 Ultra 所以没法进行双机对比
        assert_true(is_empty(result.data.item_list))

    def test01(self):
        path = "test/test_data/530/0_首轮双机对比-清晰的正确SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq(2, len(result.data.item_list))
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test1(self):
        # Run the async main function
        path = "test/test_data/530/1_首轮双机对比-错误SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test2(self):
        # Run the async main function
        path = "test/test_data/530/2_首轮双机对比-模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test3(self):
        # Run the async main function
        path = "test/test_data/530/3_首轮双机对比-单竞品.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 15 Ultra", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 15 Ultra", "Samsung Galaxy S25 Ultra"], item_name_list)
        print(reason)
        assert_true(is_eq)

    def test4(self):
        # Run the async main function
        path = "test/test_data/530/4_首轮双机对比-双竞品.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test5(self):
        path = "test/test_data/530/5_首轮无SPU-数码知识.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.FREE_FAQ_ANSWER, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test6(self):
        path = "test/test_data/530/6_首轮无SPU-时效性问题.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test7(self):
        path = "test/test_data/530/7_首轮无SPU-超纲问题.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test8(self):
        path = "test/test_data/530/8_首轮无SPU-闲聊对话.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.FREE_FAQ_ANSWER, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test9(self):
        path = "test/test_data/530/9_首轮无SPU-模糊意图.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test10(self):
        path = "test/test_data/530/10_首轮模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        for item in result.data.item_list:
            assert_true(item.item_name.startswith("Redmi"))

    def test11(self):
        path = "test/test_data/530/11_首轮清晰SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)

    def test12(self):
        path = "test/test_data/530/12_次轮-无主语.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)

    def test13(self):
        path = "test/test_data/530/13_次轮-清晰SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15 Ultra", result.data.selected_item.item_name)

    def test14(self):
        path = "test/test_data/530/14_次轮-模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)
        for item in result.data.item_list:
            assert_true(item.item_name.startswith("Redmi"))

    # 重要程度：1
    def test15(self):
        path = "test/test_data/530/15_双机对比_1.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Redmi Note 13", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Redmi Note 13", "Redmi Note 14 5G"], item_name_list)
        print(reason)
        assert_true(is_eq)

    def test16(self):
        path = "test/test_data/530/16_双机对比_2.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item.item_name in ["Redmi Note 14 5G", "Redmi Note 14"])
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Redmi Note 14 5G", "Redmi Note 14"], item_name_list)
        print(reason)
        assert_true(is_eq)

    # 重要程度：1
    def test17(self):
        path = "test/test_data/530/17_双机对比_3.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 14", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 14", "Xiaomi 15"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test18(self):
        path = "test/test_data/530/18_时效性.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        prompt = f"""判断以下内容中是否有和时效性相关的信息（比如「redmi 13 是小米最新的手机」），严格只返回是或否，不好包含其他内容:
{result.data.text}
回答："""
        assert_false_by_llm(prompt)

    def test19(self):
        path = "test/test_data/多轮对话/1_补充问题.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 15", "Circle to Search", "Cimahi"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test20(self):
        path = "test/test_data/多轮对话/2_切换维度.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 14T", "AMOLED"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test21(self):
        path = "test/test_data/多轮对话/3_深入追问.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Redmi Note 14 Pro+ 5G", "200MP"]:
            assert_true(keyword.lower() in result.data.text.lower())

    # 这个测试用例可能有问题
    @unittest.skip("skip")
    def test22(self):
        path = "test/test_data/多轮对话/4_切换维度.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 15", "Leica"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test23(self):
        path = "test/test_data/多轮对话/5_fabe不确定机型.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, answer_elapse_threshold=LONG_ANSWER_ELAPSE_THRESHOLD)
        pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Redmi Note 14", "FABE"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test24(self):
        # test long answer
        result = direct_chat_to_copilot("Xiaomi15Ultra有哪些卖点，按FABE要素描述")
        assert_elapse(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 15 Ultra", "FABE"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test25(self):
        result = direct_chat_to_copilot("iPhone 13 好用吗？")
        assert_elapse(result, answer_elapse_threshold=LONG_ANSWER_ELAPSE_THRESHOLD)
        assert_eq(MessageType.TEXT, result.data.answer_type)

    def test26(self):
        result = direct_chat_to_copilot("Xiaomi 15 vs 14")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        for keyword in ["Xiaomi 15", "Xiaomi 14"]:
            assert_true(keyword.lower() in result.data.text.lower())
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test27(self):
        result = direct_chat_to_copilot("15和14的电池对比")
        assert_elapse(result)
        pprint(result)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)

    def test28(self):
        result = direct_chat_to_copilot("redmi 屏幕多大？")
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_true(len(result.data.item_list) > 0)

    def test29(self):
        path = "test/test_data/多轮对话/6_谢谢.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        # pprint(result)
        assert_eq(MessageType.FREE_FAQ_ANSWER, result.data.answer_type)

    def test30(self):
        path = "test/test_data/多轮对话/7_闲聊对话而不是双机对比.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        # pprint(result)
        assert_true(result.data.answer_type in (MessageType.FREE_FAQ_ANSWER, MessageType.FREE_FAQ_REJECT))

    def test31(self):
        path = "test/test_data/多轮对话/8_他们哪个续航比较长.json"
        result = direct_chat_to_copilot_from_file(path)
        # pprint(result)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Redmi 13C", "POCO M3 Pro 5G"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test32(self):
        # 机型不支持拒答
        result = direct_chat_to_copilot("小米16 pro 电池多少？")
        assert_eq(MessageType.NOT_SUPPORTED_ITEM, result.data.answer_type)
        assert_elapse(result)
        result = direct_chat_to_copilot("Redmi watch 5 续航多久？")
        assert_eq(MessageType.NOT_SUPPORTED_ITEM, result.data.answer_type)
        assert_elapse(result)
        result = direct_chat_to_copilot("Smart band 9 有哪些颜色？")
        assert_eq(MessageType.NOT_SUPPORTED_ITEM, result.data.answer_type)
        assert_elapse(result)

    def test33(self):
        # 两个机型正确，进行双机对比
        result = direct_chat_to_copilot("Xiaomi 13T 和 14 哪个屏幕大些")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 13T", "Xiaomi 14"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)
        # 一个机型错误，直接让用户跳转
        result = direct_chat_to_copilot("Xiaomi 13 和 14 哪个屏幕大些")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 13T", "Xiaomi 14"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test34(self):
        # 机型比较笼统
        result = direct_chat_to_copilot("小米手机cpu？")
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_elapse(result)

    def test35(self):
        path = "test/test_data/多轮对话/9_夜拍效果对比.json"
        result = direct_chat_to_copilot_from_file(path)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 13T", "Xiaomi 15"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result, first_token_elapse_threshold=LONG_FIRST_TOKEN_ELAPSE_THRESHOLD)

    def test36(self):
        path = "test/test_data/多轮对话/10_多轮对话中的双机对比.json"
        result = direct_chat_to_copilot_from_file(path)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 15 Ultra", "Xiaomi 14"], item_name_list)
        assert_true(is_eq)
        assert_elapse(result)

    def test37(self):
        result = direct_chat_to_copilot("xiaomi 14t和iphone 13的屏幕对比")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        for keyword in ["Xiaomi 14T", "iPhone 13", "layar"]:
            assert_true(keyword.lower() in result.data.text.lower())
        assert_elapse(result)

    def test38(self):
        path = "test/test_data/多轮对话/12_哪个贵.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_elapse(result)

    def test39(self):
        path = "test/test_data/多轮对话/11_哪个电池容量大.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_elapse(result)

    def test40(self):
        path = "test/test_data/多轮对话/13_双机对比不改变吸顶.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 13T", result.data.selected_item.item_name)
        assert_elapse(result)

    def test41(self):
        # 找出小米手机吸顶并跳转
        result = direct_chat_to_copilot("iphone13 和 小米14 哪个好？")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 14", result.data.selected_item.item_name)
        assert_eq("Xiaomi 14", result.data.item_list[0].item_name)
        assert_eq("iPhone 13", result.data.item_list[1].item_name)
        assert_elapse(result)

    def test42(self):
        # 两个非小米机型，不进行跳转和吸顶
        result = direct_chat_to_copilot("iphone13 和 iphone14 哪个好？")
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))
        assert_elapse(result)

    def test43(self):
        invalid_list = ['   ', '!@#$%', '😀😃😄', '   ', '?']
        valid_list = ['Hello World', '你好世界', '123', 'Test123测试', 'Hello', 'café', 'Việt Nam', 'مَرْحَبًا', 'ก้อง',
                      '́́́']
        for invalid in invalid_list:
            result = direct_chat_to_copilot(invalid)
            assert_eq(MessageType.FILTERED_BY_START_RULE, result.data.answer_type)
            assert_elapse(result)

        for valid in valid_list:
            assert_true(has_valid_info(valid))

    def test44(self):
        path = "test/test_data/多轮对话/14_双机对比机型一样.json"
        result = direct_chat_to_copilot_from_file(path)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(is_empty(result.data.item_list))
        assert_elapse(result)

    def test45(self):
        path = "/Users/<USER>/workspace/inference/tmp.json"
        result = direct_chat_to_copilot_from_file(path)
        pprint(result)


if __name__ == '__main__':
    unittest.main()
