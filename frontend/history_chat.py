import getpass
import json
import socket
import uuid
import requests

import streamlit as st

from core.enum.content_type import ContentType
from core.enum.message_type import MessageType
from core.schema.chat_request import Message, ChatRequest
from core.schema.chat_round import ChatRound
from core.enum.role import Role
from core.enum.response_mode import ResponseMode
from core.enum.language import Language
from core.enum.area import Area
from core.schema.chat_response import ChatResponse
from core.schema.constant import ENV_SHOW_NAME_DICT

from frontend.base_unit import call_chat, enhance_msg, response_data_to_message, HOST_PROJECT_DIR
from util.common_util import not_empty
from util.date_util import get_cur_time_str
from util.llm_util import translate
from api.routers import SuggestQueriesRequest

SHOULD_SKIP = "SHOULD_SKIP"

def history_chat(project_dir):
    # 设置侧边栏
    env, debug_by_chinese, language = setup_sidebar(project_dir)

    # 显示“停止响应”按钮
    display_cancel_button()

    # 设置UI布局
    chat_area, question_area, suggested_queries_area = setup_ui_layout()

    init_session_state()
    show_chat_history(chat_area)

    # 显示机型选择按钮（如果需要）
    display_item_selection_buttons()

    # 在页面底部显示预设问题按钮和输入框
    user_input = st.chat_input("What is up?", key='prompt')

    # 显示预设问题按钮
    display_preset_questions(question_area)

    # 显示推荐的问题按钮
    display_suggested_queries(suggested_queries_area)

    # 处理机型选择
    process_item_selection(chat_area, env, debug_by_chinese, language)

    # 处理预设问题选择
    process_selected_question(chat_area, env, debug_by_chinese, language)

    # 处理用户点击推荐问题
    process_selected_suggested_query(chat_area, env, debug_by_chinese, language)

    # 处理用户输入
    process_user_input(user_input, chat_area, env, debug_by_chinese, language)


def setup_sidebar(project_dir):
    """设置侧边栏UI组件"""
    sidebar = st.sidebar

    with sidebar:
        should_translate_to_chinese = st.checkbox("自动翻译成中文", value=False, key='debug_by_chinese')
        # 机型不再由用户选择，而是由服务器返回
        # 显示当前服务器返回的机型（如果有）
        if "server_selected_item" not in st.session_state:
            st.session_state.server_selected_item = None

        col1, col2 = st.columns(2)
        with col1:
            if st.session_state.server_selected_item:
                st.info(f"当前机型: {st.session_state.server_selected_item.item_name}（如更新不及时，请刷新页面👉🏻）")
            else:
                st.info("尚未确定机型，请开始对话（如更新不及时，请刷新页面👉🏻）")
        with col2:
            st.button("刷新页面")

        # ToDo(hm): 默认环境设置可以从文件中读取，这样就不用来回切换了
        if project_dir == HOST_PROJECT_DIR:
            # demo 机器环境
            env_default_idx = 2
        else:
            # 默认选中本地
            env_default_idx = 0
        language = st.selectbox("设置语言", Language.mapping().keys(), key='lan')
        env = st.selectbox("选择环境", ["本地", "测试", "预发", "生产"], key='env', index=env_default_idx)
        env = ENV_SHOW_NAME_DICT[env]
        # debug_by_chinese = language == "中文"
        # 添加一个分隔线
        st.markdown("---")

        display_chat_request_button()
    return env, should_translate_to_chinese, Language.from_string(language)


def display_cancel_button():
    """在侧边栏显示“停止响应”按钮，并处理点击事件"""
    with st.sidebar:
        st.markdown("---")  # 分隔线
        if st.button("🚫 停止响应"):
            if "current_request_id" in st.session_state and st.session_state.current_request_id:
                cancel_request_id = st.session_state.current_request_id
                cancel_url = "http://0.0.0.0:7888/api/v1/cancel"  # 替换为你的 /cancel 端点的实际 URL

                # 构建取消请求的负载
                payload = {
                    "request_id": cancel_request_id
                }

                try:
                    response = requests.post(cancel_url, json=payload)
                    if response.status_code == 200:
                        cancel_response = response.json()
                        if cancel_response.get("success"):
                            st.sidebar.success(f"请求 {cancel_request_id} 已成功取消。")
                        else:
                            st.sidebar.error(f"取消请求失败：{cancel_response}")
                    else:
                        st.sidebar.error(f"请求失败，状态码：{response.status_code}")
                except Exception as e:
                    st.sidebar.error(f"发送取消请求时出错：{e}")
            else:
                st.sidebar.warning("当前没有正在处理的请求可以取消。")

def display_suggested_queries(suggested_queries_area):
    """在指定区域显示推荐的问题按钮"""
    if st.session_state.suggested_queries:
        with suggested_queries_area:
            st.markdown("### 推荐的问题：")
            # 根据推荐问题的数量，动态创建列
            cols = st.columns(min(3, len(st.session_state.suggested_queries)))

            for i, query in enumerate(st.session_state.suggested_queries):
                col = cols[i % len(cols)]
                if col.button(query, key=f"suggested_query_{i}"):
                    st.session_state.selected_question = query
                    st.rerun()

def fetch_suggested_queries(chat_request: SuggestQueriesRequest):
    suggest_queries_url = "http://0.0.0.0:7888/api/v1/suggest_queries"  # 替换为实际的URL

    payload = chat_request.to_dict()
    try:
        response = requests.post(suggest_queries_url, data=json.dumps(payload))
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                st.session_state.suggested_queries = data.get("suggest_queries", [])
            else:
                st.session_state.suggested_queries = []
                st.error("获取推荐问题失败。")
        else:
            st.session_state.suggested_queries = []
            st.error(f"请求失败，状态码：{response.status_code}")
    except Exception as e:
        st.session_state.suggested_queries = []
        st.error(f"请求推荐问题时出错：{e}")


def display_chat_request_button():
    """显示最近的chat_request按钮和内容"""
    if "last_chat_request" in st.session_state and st.session_state.last_chat_request is not None:
        # 添加一个按钮来切换显示/隐藏chat_request
        if st.button("显示/隐藏最近的chat_request"):
            st.session_state.show_chat_request = not st.session_state.show_chat_request

        # 只有当show_chat_request为True时才显示chat_request内容
        if st.session_state.show_chat_request:
            # 将chat_request转换为JSON字符串，并格式化以便于阅读
            chat_request_json = json.dumps(st.session_state.last_chat_request.to_dict(), indent=2,
                                           ensure_ascii=False)

            # 添加下载按钮
            st.download_button(
                label="下载最近的chat_request",
                data=chat_request_json,
                file_name=f"chat_request_{get_cur_time_str()}.json",
                mime="application/json"
            )

            # 显示JSON内容
            st.code(chat_request_json, language="json")


def setup_ui_layout():
    """设置UI布局并返回各个容器"""
    header = st.container()
    chat_area = st.container()
    question_area = st.container()
    suggested_queries_area = st.container()

    # 在页面顶部显示标题
    with header:
        st.title("国际促销员 Copilot (多轮对话版本)")

    return chat_area, question_area, suggested_queries_area


def display_item_selection_buttons():
    """显示机型选择按钮（如果需要）"""
    # 如果正在等待用户选择机型且answer_type为ITEM_CANDIDATE(9)，显示选择按钮
    if st.session_state.waiting_for_item_selection and st.session_state.item_list and st.session_state.answer_type == MessageType.ITEM_CANDIDATE:
        st.markdown("### 请选择一个机型继续对话：")
        cols = st.columns(min(3, len(st.session_state.item_list)))

        for i, item in enumerate(st.session_state.item_list):
            col_index = i % len(cols)
            if cols[col_index].button(item.item_name, key=f"item_{i}", use_container_width=True):
                st.session_state.item_selection = item
                st.session_state.waiting_for_item_selection = False
                st.rerun()


def display_preset_questions(question_area):
    """显示预设问题按钮"""
    with question_area:
        st.write("常见问题：")
        col1, col2, col3 = st.columns(3)

        # Define the built-in questions
        question1 = "xiaomi 14 和 15 哪个屏幕大？"
        question2 = "note 13 手机重量是多少？"
        question3 = "xiaomi 14t 电池续航多少？"

        col4, col5, col6 = st.columns(3)
        question4 = "Layar mana yang lebih besar, Xiaomi 14 dan 15?"
        question5 = "berapa berat ponsel note 13?"
        question6 = "berapa lama baterai xiaomi 14t tahan?"

        # Create buttons for each question
        if col1.button(question1):
            st.session_state.selected_question = question1
            st.rerun()

        if col2.button(question2):
            st.session_state.selected_question = question2
            st.rerun()

        if col3.button(question3):
            st.session_state.selected_question = question3
            st.rerun()

        if col4.button(question4):
            st.session_state.selected_question = question4
            st.rerun()

        if col5.button(question5):
            st.session_state.selected_question = question5
            st.rerun()

        if col6.button(question6):
            st.session_state.selected_question = question6
            st.rerun()


def process_item_selection(chat_area, env, debug_by_chinese, language):
    if st.session_state.item_selection is None:
        return

    """处理用户选择的机型"""
    # Reset item_selection to avoid processing it again
    item_selection = st.session_state.item_selection
    st.session_state.item_selection = None
    # 将用户选择的机型保存到 server_selected_item
    st.session_state.server_selected_item = item_selection

    # Generate a request ID
    request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"
    st.session_state.current_request_id = request_id

    # Display user's item selection in chat message container
    with chat_area:
        with st.chat_message("user"):
            selection_message = f"用户选择了: {item_selection.item_name}"
            st.markdown(f'({selection_message} chatRequestId: "{request_id}")')

        # Display assistant response in chat message container
        with st.chat_message("assistant"):
            # Create a message with the selected item
            # Convert Item objects to dictionaries to avoid validation errors
            item_message = Message(
                content="",
                type=MessageType.ITEM_CONFIRM,
                item_list=[item_selection.model_dump()]
            )
            st.session_state.messages.append(
                {"role": "user", "content": selection_message, "message_obj": item_message, SHOULD_SKIP: False})

            # Convert chat history to ChatRound objects
            chat_rounds = convert_history_to_chat_rounds(st.session_state.messages)

            # Add the item selection message
            chat_rounds.append(
                ChatRound(
                    role=Role.USER,
                    messages=[item_message]
                )
            )

            # Reset waiting_for_item_selection flag
            st.session_state.waiting_for_item_selection = False

            # Make the request and process the response
            response = process_chat_request(chat_rounds, env, request_id, language)
            process_chat_response(response, debug_by_chinese, language)


def process_selected_question(chat_area, env, debug_by_chinese, language):
    """处理用户选择的预设问题"""
    button_prompt = st.session_state.selected_question
    # Reset selected_question to avoid processing it again
    st.session_state.selected_question = None
    process_user_input(button_prompt, chat_area, env, debug_by_chinese, language)


def process_user_input(user_input, chat_area, env, debug_by_chinese, language):
    if not user_input:
        return

    # Add user message to chat history
    message_obj = Message(
        type=MessageType.TEXT,
        content=user_input,
        selected_item=st.session_state.server_selected_item,
    )
    st.session_state.messages.append(
        {"role": "user", "content": user_input, "message_obj": message_obj, SHOULD_SKIP: debug_by_chinese})
    translated_prompt = user_input
    if debug_by_chinese:
        translated_prompt = translate(user_input)
        message_obj = Message(
            type=MessageType.TEXT,
            content=translated_prompt,
            selected_item=st.session_state.server_selected_item,
        )
        # convert_history_to_chat_rounds 会排除掉 translated_prompt
        st.session_state.messages.append(
            {"role": "user", "content": translated_prompt, "message_obj": message_obj, SHOULD_SKIP: False})

    # Generate a request ID
    request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"
    st.session_state.current_request_id = request_id

    # Display user message in chat message container
    with chat_area:
        with st.chat_message("user"):
            st.markdown(f'{user_input} (chatRequestId: "{request_id}")')
            if debug_by_chinese:
                st.markdown(f'{translated_prompt} (chatRequestId: "{request_id}")')

        # Display assistant response in chat message container
        with st.chat_message("assistant"):
            # Convert chat history to ChatRound objects
            chat_rounds = convert_history_to_chat_rounds(st.session_state.messages)

            # Make the request and process the response
            response = process_chat_request(chat_rounds, env, request_id, language)
            process_chat_response(response, debug_by_chinese, language)


def process_chat_response(response, debug_by_chinese, language):
    """处理聊天响应并更新UI"""
    response_data = response.data
    st.session_state.server_selected_item = response_data.selected_item
    st.session_state.answer_type = response_data.answer_type

    # Generate and store the enhanced message
    enhanced_message = enhance_msg(response)
    st.session_state.enhanced_msg = enhanced_message

    # Display the response (with HTML support for doc trace sources)
    st.markdown(enhanced_message, unsafe_allow_html=True)
    st.markdown("#### 答案溯源")
    for doc_trace_source in response_data.doc_trace_source_list:
        if doc_trace_source.content_type == ContentType.HTML:
            st.markdown(f"##### {doc_trace_source.title}({doc_trace_source.doc_type.name})")
            st.html(doc_trace_source.content)
            continue

        if doc_trace_source.content_type == ContentType.URL:
            st.markdown(f"##### {doc_trace_source.title}({doc_trace_source.doc_type.name})")
            st.markdown(f"[{doc_trace_source.content}]({doc_trace_source.content})")
            continue

        if doc_trace_source.content_type == ContentType.KEY:
            st.markdown(f"##### {doc_trace_source.title}({doc_trace_source.doc_type.name})")
            st.markdown(doc_trace_source.content)
            continue

    # Convert ChatResponseData to Message
    message_obj = response_data_to_message(response_data)

    # Add the response to chat history with enhanced content and Message object
    st.session_state.messages.append({
        "role": "assistant",
        "content": response_data.text,
        "enhanced_content": enhanced_message,
        "message_obj": message_obj,
        SHOULD_SKIP: False
    })

    # 如果是中文模式，需要翻译响应并添加到聊天历史
    if debug_by_chinese:
        translated_response = translate(enhanced_message, from_lang="印尼语", to_lang="中文")
        st.markdown(translated_response, unsafe_allow_html=True)
        # For translated response, create a new Message with the same properties but translated content
        translated_message_obj = Message(
            type=message_obj.type,
            content=translated_response,
            selected_item=message_obj.selected_item,
            item_list=message_obj.item_list
        )

        st.session_state.messages.append({
            "role": "assistant",
            "content": translated_response,
            "enhanced_content": translated_response,
            "message_obj": translated_message_obj,
            SHOULD_SKIP: True
        })

    # If the response has item_list, set the waiting_for_item_selection flag and store the item_list
    if not_empty(response_data.item_list):
        st.session_state.waiting_for_item_selection = True
        st.session_state.item_list = response_data.item_list
        st.rerun()

    # 调用获取推荐问题的函数
    # 提取聊天历史，产品名称和竞争对手列表
    chat_history = [msg["content"] for msg in st.session_state.messages]

    # 构建一个临时的 ChatRequest 对象用于获取 request_id 等信息
    temp_chat_request = SuggestQueriesRequest(
        request_id=st.session_state.current_request_id,
        is_canceled=response_data.is_canceled if hasattr(response_data, "is_canceled") else False,
        answer_intent=response_data.answer_intent if hasattr(response_data, 'answer_intent') else "",
        language=language,
        chat_history=convert_history_to_chat_rounds(st.session_state.messages)
    )

    fetch_suggested_queries(temp_chat_request)


def process_selected_suggested_query(chat_area, env, debug_by_chinese, language):
    """处理用户点击推荐的问题"""
    if st.session_state.selected_question:
        question = st.session_state.selected_question
        st.session_state.selected_question = None
        process_user_input(question, chat_area, env, debug_by_chinese, language)


def init_session_state():
    # Initialize session state variables
    if "messages" not in st.session_state:
        st.session_state.messages = []

    if "selected_question" not in st.session_state:
        st.session_state.selected_question = None

    if "item_selection" not in st.session_state:
        st.session_state.item_selection = None

    if "item_list" not in st.session_state:
        st.session_state.item_list = None

    if "waiting_for_item_selection" not in st.session_state:
        st.session_state.waiting_for_item_selection = False

    if "current_response" not in st.session_state:
        st.session_state.current_response = ""

    if "enhanced_msg" not in st.session_state:
        st.session_state.enhanced_msg = ""

    if "answer_type" not in st.session_state:
        st.session_state.answer_type = MessageType.TEXT

    if "last_chat_request" not in st.session_state:
        st.session_state.last_chat_request = None

    if "show_chat_request" not in st.session_state:
        st.session_state.show_chat_request = False

    if "current_request_id" not in st.session_state:
        st.session_state.current_request_id = None

    if "suggested_queries" not in st.session_state:
        st.session_state.suggested_queries = []


def show_chat_history(chat_area):
    # 在聊天区域显示聊天历史
    with chat_area:
        # Display chat messages from history on app rerun
        for i, message in enumerate(st.session_state.messages):
            with st.chat_message(message["role"]):
                # For assistant messages, check if we have enhanced content
                if message["role"] == "assistant" and "enhanced_content" in message:
                    st.markdown(message["enhanced_content"], unsafe_allow_html=True)
                else:
                    st.markdown(message["content"], unsafe_allow_html=True)


def convert_history_to_chat_rounds(messages):
    """Convert the chat history from session state to ChatRound objects

    If debug_by_chinese is True, only include translated messages for user inputs
    and original messages for assistant responses.
    """
    chat_rounds = []
    current_role = None
    current_messages = []

    for i, message in enumerate(messages):
        role = message["role"]
        # Map the role from string to Role enum
        role_enum = Role.USER if role == "user" else Role.ASSISTANT

        # If debug_by_chinese is True and we're processing user messages,
        # we need special handling to only include the translated version
        if SHOULD_SKIP in message and message[SHOULD_SKIP]:
            continue

        # If the role changes, add the previous messages as a ChatRound
        if current_role is not None and current_role != role_enum:
            chat_rounds.append(
                ChatRound(
                    role=current_role,
                    messages=current_messages
                )
            )
            current_messages = []
        current_messages.append(message["message_obj"])
        current_role = role_enum

    # Add the last set of messages
    if current_role is not None and current_messages:
        chat_rounds.append(
            ChatRound(
                role=current_role,
                messages=current_messages
            )
        )

    return chat_rounds


def process_chat_request(chat_rounds, env, request_id, language) -> ChatResponse:
    """Process a chat request and return the response text, whether it has item_list, the item_list, and answer_type"""
    # Create a ChatRequest object with the entire chat history
    chat_request = ChatRequest(
        area=Area.INDONESIA,
        site=0,
        category_name="0",
        category_id="0",
        user_id="0",
        item_id="0",
        org_id="0",
        conversation_id="0",
        language=language,
        item_name="",
        chat_history=chat_rounds,
        response_mode=ResponseMode.STREAMING,
        request_id=request_id,
        debug=True,
        version=1
    )

    # 保存chat_request到session state
    st.session_state.last_chat_request = chat_request
    return call_chat(chat_request, env)
