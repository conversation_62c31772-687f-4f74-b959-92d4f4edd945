import json
import os
import re
import time
import hashlib
import uuid
from typing import List, Optional

from loguru import logger
from config.model_config import MODEL_VERSION
from core.enum.doc_type import DocType
from core.schema.doc_trace_source import DocTraceSource

from util.string_util import to_json_str
from config.prompts import HTML_TEMPLATE, SECTION_TEMPLATE


def assert_eq(expected, actual):
    if expected != actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_not_eq(expected, actual):
    if expected == actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_true(actual):
    if not actual:
        raise RuntimeError(f"expected True, actual={actual}")


def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def is_empty(item):
    return item is None or len(item) == 0


def not_empty(item):
    return not is_empty(item)


def get_cur_millis():
    return int(time.time() * 1000)


def get_elapse_str(elapse_ms):
    return f"{elapse_ms} 毫秒"


def decode_sse(raw_event_str):
    if not raw_event_str.startswith("data:"):
        return dict()

    return json.loads(raw_event_str[6:])


def get_ending_message(chat_request):
    return chat_request.chat_history[-1].messages[-1]


def may_have_bad_markdown(text):
    if is_empty(text):
        return False

    markdown_pattern = r'(markdown|Markdown|MarkDown)'
    return re.search(markdown_pattern, text) or text.startswith("```")


def pprint(data):
    print("---")
    print(to_json_str(data, intend=2))
    print("---")


def get_env_by_key(key, default=None):
    val = os.environ.get(key)
    if not val:
        logger.warning(f"环境变量 {key} 未设置，将使用默认值 {default}")
        return default

    return val


def get_chat_request_key(chat_request):
    item_name = chat_request.item_name if chat_request.item_name is not None else "空"
    joined_str_message = chat_request.joined_str_message if chat_request.joined_str_message is not None else \
        chat_request.chat_history[-1].messages[-1].content

    chat_request_key = f"item_name: {item_name}, joined_str_message: {joined_str_message}, MODEL_VERSION: {MODEL_VERSION}"
    chat_request_key = chat_request_key.encode('utf-8')
    chat_request_key = hashlib.sha256(chat_request_key).hexdigest()
    return chat_request_key


def render_page(question, answer_seg, use_html_template=False):
    answer_html_list = list()
    idx = 1
    for answer_seg_type, answer_seg_content in answer_seg:
        if answer_seg_type == 'text':
            answer_html_list.append(f"<p>{answer_seg_content}</p>")
        elif answer_seg_type == 'image':
            answer_html_list.append(
                f'<img src="{answer_seg_content}" alt="{question}_{idx}" loading="lazy" width="400" height="300">')
            idx += 1
    answer_html = '\n'.join(answer_html_list)
    section_html = SECTION_TEMPLATE.format(question=question, answer_html=answer_html)
    if not use_html_template:
        return section_html

    html = HTML_TEMPLATE.format(section_html=section_html)
    return html


def merge_faq_sources(faq_sources: List[DocTraceSource]) -> Optional[DocTraceSource]:
    if not faq_sources:
        return None

    # 使用第一个 FAQ 的基本信息
    first_faq = faq_sources[0]
    # 合并内容和原始内容
    merged_content_parts = []
    merged_raw_content_parts = []
    for faq in faq_sources:
        if faq.content:
            merged_content_parts.append(faq.content)
        if faq.raw_content:
            merged_raw_content_parts.append(faq.raw_content)

    section_html = '\n'.join(merged_content_parts) if merged_content_parts else None
    whole_html = HTML_TEMPLATE.format(section_html=section_html)
    merged_raw_content = '\n'.join(merged_raw_content_parts) if merged_raw_content_parts else None

    # 创建聚合后的 FAQ
    return DocTraceSource(
        item_id=first_faq.item_id,
        item_name=first_faq.item_name,
        doc_type=DocType.FAQ,
        title="FAQs" if len(faq_sources) > 1 else "FAQ",
        content_type=first_faq.content_type,
        content=whole_html,
        raw_content=merged_raw_content
    )


def aggregate_doc_trace_sources(doc_trace_source_list: List[DocTraceSource]) -> List[DocTraceSource]:
    """
    聚合 DocTraceSource 列表：
    1. 如果有多个 FAQ，将它们合成一个 DocTraceSource，title 统一用 "FAQs"，内容和原始内容为多个拼接
    2. 如果有多个产品参数（PRODUCT_PARAM），只保留第一个
    3. 如果有多个产品详情（PRODUCT_INTRODUCTION），只保留第一个
    4. 培训文档（SALE_TOOLS）保留所有的
    5. 整体顺序以原来的为准
    """
    if not doc_trace_source_list:
        return []

    aggregated_list = []
    faq_sources = []
    product_param_source = None
    product_intro_source = None

    # 按原始顺序遍历，收集不同类型的文档
    for doc_source in doc_trace_source_list:
        if doc_source.doc_type == DocType.FAQ:
            faq_sources.append(doc_source)
        elif doc_source.doc_type == DocType.PRODUCT_PARAM:
            if product_param_source is None:  # 只保留第一个
                product_param_source = doc_source
        elif doc_source.doc_type == DocType.PRODUCT_INTRODUCTION:
            if product_intro_source is None:  # 只保留第一个
                product_intro_source = doc_source
        elif doc_source.doc_type == DocType.SALE_TOOLS:
            # 培训文档保留所有的
            aggregated_list.append(doc_source)

    # 按原始顺序重新构建列表，保持顺序
    result = []
    faq_aggregated = False

    for doc_source in doc_trace_source_list:
        if doc_source.doc_type == DocType.FAQ:
            if not faq_aggregated and faq_sources:
                # 聚合所有 FAQ
                aggregated_faq = merge_faq_sources(faq_sources)
                result.append(aggregated_faq)
                faq_aggregated = True
        elif doc_source.doc_type == DocType.PRODUCT_PARAM:
            if doc_source == product_param_source:  # 只添加第一个
                result.append(doc_source)
        elif doc_source.doc_type == DocType.PRODUCT_INTRODUCTION:
            if doc_source == product_intro_source:  # 只添加第一个
                result.append(doc_source)
        elif doc_source.doc_type == DocType.SALE_TOOLS:
            # 培训文档保留所有的
            result.append(doc_source)
    return result


def get_tag_for_item_name(item_name):
    item_name = item_name.lower()
    item_name = item_name.replace(" ", "-")
    return item_name
