import json

from config.chat_config import ENHANCE_QUERY_POSTFIX
from config.prompts import REJECT_QA_SYSTEM_PROMPT, REJECT_QA_USER_PROMPT, FREE_QA_SYSTEM_PROMPT, FREE_QA_USER_PROMPT, \
    REFERENCE_QA_SYSTEM_PROMPT, REFERENCE_QA_USER_PROMPT, ITEM_COMPARE_SYSTEM_PROMPT, ITEM_COMPARE_USER_PROMPT
from core.schema.chat_request import ChatRequest
from core.schema.knowledge_source import KnowledgeSource
from core.schema.trace_source_info import TraceSourceInfo
from service.base_service import BaseService
from util.common_util import is_empty, not_empty
from util.mysql_db_manager import DBManager
from util.common_util import get_env_by_key

ENV_NAME = get_env_by_key("ENV_NAME", "local")


class PromptBuildService(BaseService):
    def __init__(self, item_param_config_dict, model_manager, doc_dataset_id_dict, item_name_intro_dict, db: DBManager,
                 redis_manager=None):
        super().__init__(None, None)
        self._doc_dataset_id_dict = doc_dataset_id_dict
        self._model_manager = model_manager
        self._item_name_intro_dict = item_name_intro_dict
        self._item_name_param_dict = item_param_config_dict
        self._redis_manager = redis_manager
        self._db = db

    @staticmethod
    def build_free_question_reject_prompt(chat_request: ChatRequest):
        system_prompt = REJECT_QA_SYSTEM_PROMPT.format(language=chat_request.get_final_language().get_chinese_name())
        user_prompt = REJECT_QA_USER_PROMPT.format(chat_context=chat_request.joined_str_message,
                                                   answer_intent=chat_request.answer_intent)
        chat_request.logger.info(f"system_prompt={system_prompt}")
        chat_request.logger.info(f"user_prompt={user_prompt}")
        return system_prompt, user_prompt

    @staticmethod
    def build_free_question_prompt(chat_request: ChatRequest):
        system_prompt = FREE_QA_SYSTEM_PROMPT.format(language=chat_request.get_final_language().get_chinese_name())
        user_prompt = FREE_QA_USER_PROMPT.format(chat_context=chat_request.joined_str_message)
        chat_request.logger.info(f"system_prompt={system_prompt}")
        chat_request.logger.info(f"user_prompt={user_prompt}")
        return system_prompt, user_prompt

    # second_tag_map_dict is sub dict of SECOND_TAG_DICT
    def build_prompt(self, chat_request: ChatRequest, knowledge_dict):
        chat_request.logger.info(chat_request.str_message_list)
        # get ending round from chat_history
        knowledge_doc_all = knowledge_dict.get(KnowledgeSource.DOC)
        knowledge_doc = list()
        knowledge_faq = list()
        if not_empty(knowledge_doc_all):
            for data_dict in knowledge_doc_all:
                doc_key = None
                if "doc_key" in data_dict:
                    doc_key = data_dict["doc_key"]
                line = data_dict["content"]
                score = data_dict["score"]
                if is_empty(line):
                    continue

                split_data = line.split("\n")
                if is_empty(split_data):
                    continue

                # 去掉前后的标签
                stripped = "\n".join(split_data[1:-1])
                if split_data[0] == "<FAQ>":
                    knowledge_faq.append((score, stripped))
                    continue

                if split_data[0] == "<SALE TOOLS>" and doc_key is not None:
                    knowledge_doc.append((score, stripped, doc_key))
                    continue

        # 用于答案溯源的信息
        trace_source_info = TraceSourceInfo()
        knowledge_xml_list = list()
        high_priority_list = list()
        if not_empty(knowledge_doc):
            knowledge_doc.sort(key=lambda x: x[0], reverse=True)
            chat_request.logger.debug(f"knowledge_doc={len(knowledge_doc)}")
            final_knowledge_doc = [(element[1], element[2]) for element in knowledge_doc]
            filtered_knowledge_doc = final_knowledge_doc[:3]
            for content, doc_key in filtered_knowledge_doc:
                if doc_key not in trace_source_info.sale_tool_dict:
                    trace_source_info.sale_tool_dict[doc_key] = list()
                trace_source_info.sale_tool_dict[doc_key].append(content)
            raw_content = "\n".join([element[0] for element in filtered_knowledge_doc])
            knowledge_xml_list.append(f"<培训材料>\n{raw_content}\n</培训材料>")
        if not_empty(knowledge_faq):
            knowledge_faq.sort(key=lambda x: x[0], reverse=True)
            chat_request.logger.debug(f"knowledge_faq={len(knowledge_faq)}")
            final_knowledge_faq = [element[1] for element in knowledge_faq]
            filtered_faq_list = final_knowledge_faq[:3]
            trace_source_info.faq_list.extend(filtered_faq_list)
            raw_content = "\n".join(filtered_faq_list)
            knowledge_xml_list.append(f"<常见问题>\n{raw_content}\n</常见问题>")
        knowledge_intro = self._item_name_intro_dict.get(chat_request.item_name_normalized)
        if is_empty(knowledge_intro):
            chat_request.logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网介绍文案")
        else:
            trace_source_info.intro_list.extend(knowledge_intro)
            raw_content = "\n".join(knowledge_intro)
            knowledge_xml_list.append(f"<宣传材料>\n{raw_content}\n</宣传材料>")
            high_priority_list.append("<宣传材料>")
        knowledge_param = []
        if chat_request.item_name_normalized in self._item_name_param_dict:
            knowledge_param = self._item_name_param_dict.get(chat_request.item_name_normalized)
        else:
            # ToDo(hm): 如何处理这里的答案溯源，问下帅哥
            knowledge_db_result = self._db.query_data_from_product_info(product_id=chat_request.item_name_normalized)
            if knowledge_db_result:
                knowledge_param_dict = {}
                for first_key, second_key, value in knowledge_db_result:
                    if value and len(value) > 0:
                        if first_key in knowledge_param_dict:
                            knowledge_param_dict[first_key] = knowledge_param_dict[
                                                                  first_key] + " " + second_key + " " + value
                        else:
                            knowledge_param_dict[first_key] = second_key + " " + value
                knowledge_param = [k + ": " + v for k, v in knowledge_param_dict.items()]
        if is_empty(knowledge_param):
            chat_request.logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网参数")
        else:
            trace_source_info.param_list.extend(knowledge_param)
            raw_content = "\n".join(knowledge_param)
            knowledge_xml_list.append(f"<参数信息>\n{raw_content}\n</参数信息>")
            high_priority_list.append("<参数信息>")
        priority_str = ""
        if not_empty(high_priority_list):
            priority_str = f"(优先使用{"、".join(high_priority_list)}中的信息)"
        knowledge_all_str = "\n".join(knowledge_xml_list)
        system_prompt = REFERENCE_QA_SYSTEM_PROMPT
        user_prompt = REFERENCE_QA_USER_PROMPT.format(item_name=chat_request.item_name,
                                                      knowledge_all_str=knowledge_all_str,
                                                      priority_str=priority_str,
                                                      language=chat_request.get_final_language().get_chinese_name(),
                                                      answer_intent=chat_request.get_intent_prompt(),
                                                      chat_context=chat_request.joined_str_message)
        chat_request.logger.info(f"system_prompt len={len(system_prompt)}")
        chat_request.logger.info(f"system_prompt={system_prompt}")
        chat_request.logger.info(f"user_prompt len={len(user_prompt)}")
        chat_request.logger.info(f"user_prompt={user_prompt}")
        return system_prompt, user_prompt, trace_source_info

    def build_item_compare_prompt(self, chat_request: ChatRequest, item_name_list):
        item_param0 = {}
        knowledge_db_result = self._db.query_data_from_product_info(product_name=item_name_list[0])
        if knowledge_db_result is None:
            return None, None
        for first_key, second_key, value in knowledge_db_result:
            if first_key in item_param0:
                item_param0[first_key][second_key] = value
            else:
                item_param0[first_key] = {second_key: value}

        item_param1 = {}
        knowledge_db_result = self._db.query_data_from_product_info(product_name=item_name_list[1])
        if knowledge_db_result is None:
            return None, None
        for first_key, second_key, value in knowledge_db_result:
            if first_key in item_param1:
                item_param1[first_key][second_key] = value
            else:
                item_param1[first_key] = {second_key: value}
        system_prompt = ITEM_COMPARE_SYSTEM_PROMPT
        user_prompt = ITEM_COMPARE_USER_PROMPT.format(item_name0=item_name_list[0],
                                                      item_info0=json.dumps(item_param0, indent=2, ensure_ascii=False),
                                                      item_name1=item_name_list[1],
                                                      item_info1=json.dumps(item_param1, indent=2, ensure_ascii=False),
                                                      chat_context=chat_request.joined_str_message,
                                                      language=chat_request.get_final_language().get_chinese_name())
        chat_request.logger.info(f"system_prompt len={len(system_prompt)}")
        chat_request.logger.info(f"system_prompt={system_prompt}")
        chat_request.logger.info(f"user_prompt len={len(user_prompt)}")
        chat_request.logger.info(f"user_prompt={user_prompt}")
        return system_prompt, user_prompt

    @staticmethod
    def enhance_retrieval_query(chat_request, query, second_tag_map_dict):
        if is_empty(second_tag_map_dict):
            return query

        # 关于 xxx
        postfix = f"{ENHANCE_QUERY_POSTFIX[chat_request.get_final_language()]}{", ".join(second_tag_map_dict.values())}"
        return f"{query}({postfix})"

    async def retrieve_doc_knowledge(self, chat_request, query, second_tag_map_dict, item_name_normalized):
        enhanced_query = self.enhance_retrieval_query(chat_request, query, second_tag_map_dict)
        chat_request.logger.debug(f"用来召回知识的 query={enhanced_query}")
        # different spu use different dataset_id
        if item_name_normalized not in self._doc_dataset_id_dict:
            chat_request.logger.warning(f"没有找到{item_name_normalized}对应的知识（dataset id 缺失）")
            return list()

        doc_dataset_id = self._doc_dataset_id_dict[item_name_normalized]
        chat_request.logger.debug(f"召回{item_name_normalized}对应的知识，dataset_id={doc_dataset_id}")
        return await self._model_manager.retrieve_knowledge_with_score(enhanced_query, doc_dataset_id)
