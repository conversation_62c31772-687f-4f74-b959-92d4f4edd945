import asyncio
import json
import os
import aiohttp
import logging
import traceback

from core.enum.call_status import CallStatus
from core.schema.chat_request import ChatRequest
from core.schema.task import Task
from service.model_manager import ModelManager, async_retry
from util.common_util import get_cur_millis
from util.exceptions import RequestCancelledException

# 常量定义
MIFY_WORKFLOW_URL = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
RETRIEVAL_URL_TEMPLATE = "https://mify-be.pt.xiaomi.com/api/v1/datasets/{dataset_id}/retrieve"
MIFY_STOP_WORKFLOW_URL = "https://mify-be.pt.xiaomi.com/api/v1/workflows/tasks/:{task_id}/stop"

RETRIEVAL_NUM = 20
RETRIEVAL_THRESHOLD = 0
TIMEOUT_MIFY_RETRIEVAL = int(os.environ.get("TIMEOUT_MIFY_RETRIEVAL", 30))  # 秒
TIMEOUT_JSON_LLM = int(os.environ.get("TIMEOUT_JSON_LLM", 50))  # 秒
TIMEOUT_LLM = int(os.environ.get("TIMEOUT_LLM", 50))  # 秒
TIMEOUT_TRANSLATE = int(os.environ.get("TIMEOUT_TRANSLATE", 300))  # 秒

# 重试参数
MAX_RETRY_SIZE = 1
RETRY_DELAY = 1  # 秒


class ModelManagerMify(ModelManager):
    def __init__(self, counter=None, timer=None, dataset_key=None, redis_manager=None, api_key=None):
        super().__init__(counter, timer)
        self._dataset_key = dataset_key
        self._redis_manager = redis_manager
        self._api_key = api_key

    # TODO(jxy) 由于流式调用出错，暂时不增加调用失败重试的逻辑
    # @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def call_llm_with_stream(self, system_prompt, user_prompt, chat_request):
        if self._redis_manager is not None and await should_cancel(self._redis_manager, chat_request):
            chat_request.logger.info(f"请求被取消，无需调用大模型")
            raise RequestCancelledException(request_id=chat_request.request_id)

        headers = {
            "Authorization": f"Bearer {self._api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt
            },
            "response_mode": "streaming",
            "user": f"chat_request_id-{chat_request.request_id}",
        }
        chat_request.logger.info(f"调用大模型回答问题")
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    json=body,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_LLM)
            ) as response:
                if response.status != 200:
                    error_msg = f"回答时底层大模型请求失败，状态码={response.status}，api_key={self._api_key}, 响应内容: {await response.text()}"
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=error_msg,
                    )

                async for line in response.content:
                    yield line

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def call_llm_with_json(self, system_prompt, user_prompt, api_key, chat_request: ChatRequest=None):
        if (self._redis_manager is not None and chat_request is not None and
                await should_cancel(self._redis_manager, chat_request)):
            raise RequestCancelledException(request_id=chat_request.request_id)

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt
            },
            "response_mode": "blocking",
            "user": f"chat_request_id-{chat_request.request_id}",
        }

        task = asyncio.current_task()
        call_info = None
        if isinstance(task, Task):
            call_info = task.call_info
            call_info.call_llm_time = get_cur_millis()
            call_info.system_prompt = system_prompt
            call_info.user_prompt = user_prompt
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    data=json.dumps(body),
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_JSON_LLM)
            ) as response:
                if response.status != 200:
                    if call_info:
                        call_info.status = CallStatus.FAILED
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"底层大模型请求失败（json），状态码={response.status}, api_key={api_key}，响应内容: {error_text}",
                    )

                try:
                    result = await response.json()
                    answer = result["data"]["outputs"]["answer"]
                    if call_info:
                        total_tokens = result["data"]["total_tokens"]
                        call_info.answer = answer
                        call_info.token_count = total_tokens
                        call_info.llm_response_time = get_cur_millis()
                        call_info.status = CallStatus.FINISHED
                    return True, answer
                except Exception as e:
                    raise RuntimeError(f"解析大模型结果失败 {e}: {traceback.format_exc()}")

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def translate_any2zn(self, content, api_key, request_id="unknown"):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "inputs": {
                "content": content,
            },
            "response_mode": "blocking",
            "user": f"translate_request_id-{request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_TRANSLATE)
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"translate_any2zn_async 请求失败，状态码={response.status}，api_key={api_key}, 响应内容: {error_text}",
                    )

                try:
                    result = await response.json()
                    translated_text = result["data"]["outputs"]["answer"]
                    return translated_text
                except Exception as e:
                    raise RuntimeError(f"解析大模型结果失败 {e}: {traceback.format_exc()}")

    @staticmethod
    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def stop_task(task_id: str, api_key: str, chat_request_id: str):
        """
        发送停止任务的请求到大模型平台。
        """
        url = MIFY_STOP_WORKFLOW_URL.format(task_id=task_id)
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "user": f"chat_request_id-{chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    url,
                    headers=headers,
                    json=body,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_LLM)
            ) as response:
                if response.status != 200:
                    error_msg = f"Failed to stop task {chat_request_id}: HTTP {response.status}"
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=error_msg
                    )

                result = await response.json()
                if result.get("result") == "success":
                    logging.info(f"Successfully stopped task {chat_request_id}.")
                    return True

                error_msg = f"Failed to stop task {chat_request_id}: {result}"
                logging.error(error_msg)
                raise RuntimeError(error_msg)


if __name__ == '__main__':
    # 配置基本日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("ModelManager")

    # 实例化 ModelManager
    model_manager = ModelManager(logger=logger)
    app_key = "app-pyMZ9rscnYtlmQbl72LskqXl"
    # 测试同步调用
    try:
        result = model_manager.call_llm_sync("system_prompt", "hi", app_key)
        print(result)
    except Exception as e:
        print(f"同步调用失败: {e}")


    # 若需测试异步函数，可以使用以下代码：
    async def test_async():
        try:
            async for line in model_manager.call_llm_with_stream("system_prompt", "hi", app_key):
                print(line)
        except Exception as e:
            print(f"异步流式调用失败: {e}")

        try:
            knowledge = await model_manager.retrieve_knowledge_with_score("query",
                                                                          "64083b09-9ad3-477e-a7b6-84174387084f")
            print(knowledge)
        except Exception as e:
            print(f"获取知识带分数失败: {e}")

        try:
            knowledge = await model_manager.retrieve_knowledge_from_mify("query",
                                                                         "64083b09-9ad3-477e-a7b6-84174387084f")
            print(knowledge)
        except Exception as e:
            print(f"从 Mify 获取知识失败: {e}")

        try:
            response = await model_manager.call_llm_with_json("system_prompt", "user_prompt", app_key)
            print(response)
        except Exception as e:
            print(f"调用 LLM (JSON) 失败: {e}")

        try:
            translation = await model_manager.translate_any2zn("content", "app-tW5Sya7MmJieHKKD12TGMQFr")
            print(translation)
        except Exception as e:
            print(f"翻译失败: {e}")

        try:
            llm_async = await model_manager.call_llm_async("system_prompt", "user_prompt", app_key)
            print(llm_async)
        except Exception as e:
            print(f"异步调用 LLM 失败: {e}")


    asyncio.run(test_async())


async def should_cancel(redis_manager, chat_request: ChatRequest) -> bool:
    if chat_request is None:
        return False

    if await chat_request.http_request.is_disconnected():
        # 如果客户端断开连接，后面流程也终止
        chat_request.logger.info(f"客户端断开连接，后面流程也终止")
        return True

    if redis_manager is None:
        return False

    try:
        key = f"request:{chat_request.request_id}"
        # 从 Redis 读取 should_cancel 字段
        canceled = redis_manager.hget(key, "should_cancel")
        return canceled == "True"
    except Exception as e:
        chat_request.logger.error(f"检查取消状态时发生错误: {e}")
        return False
