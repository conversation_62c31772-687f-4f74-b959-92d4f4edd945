import datetime
import json
import time
import traceback
import uuid
import async<PERSON>
from typing import Optional

from prometheus_client import Counter, Histogram

from config.model_config import MODEL_VERSION
from config.run_config import RUN_CONFIG_DICT, DATA_TABLE_NAME
from config.chat_config import REFUSAL_MESSAGE_DICT, PRE_MESSAGE_DICT
from core.enum.call_status import CallStatus
from core.enum.pre_thinking_message_type import PreThinkingMessageType
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import ChatResponse
from core.enum.event_type import EventType
from core.schema.chat_response_data import ChatResponseData
from core.enum.message_type import MessageType
from core.schema.constant import CHAT, ENV_NAME_KEY, TEST_ENV
from core.schema.db_record import DBRecord
from core.schema.item import Item
from service.base_service import BaseService
from service.model_manager import Model<PERSON>anager, should_cancel
from service.model_manager_mify import Model<PERSON>anagerMify
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from service.task_manager import Task<PERSON>anager
from util.common_util import decode_sse, get_cur_millis, is_empty, get_env_by_key, get_elapse_str, not_empty
from util.mysql_db_manager import DBManager
from util.exceptions import RequestCancelledException
from util.string_util import to_json_str
from core.processor import normalize_item_name, get_str_message_list
from config.prompts import INTENT_CATEGORY_DICT, FREE_QA_CAT, REJECT_QA_CAT, SUGGEST_QUERIES_KEY, \
    QUERIES_ABOUT_CURRENT_ITEM, COMPARISON_WITH_COMPETITORS, SUGGEST_QUERIES_SYSTEM_PROMPT, SUGGEST_QUERIES_USER_PROMPT

import random

ENV_NAME = get_env_by_key(ENV_NAME_KEY, TEST_ENV)


class ChatServiceBase(BaseService):
    def __init__(self, counter: Counter, timer: Histogram, api_key_text, query_parse_service: QueryParseService,
                 prompt_build_service: PromptBuildService, model_manager: ModelManager, normalized_item_name_list,
                 item_name_xiaomi_list, db: DBManager, name_item_dict, redis_manager):
        super().__init__(counter, timer)
        self._api_key_text = api_key_text
        self._query_parse_service: QueryParseService = query_parse_service
        self._prompt_build_service: PromptBuildService = prompt_build_service
        self._model_manager: ModelManager = model_manager
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list
        self._db = db
        self._name_item_dict = name_item_dict
        self._redis_manager = redis_manager
        self._data_table_name = RUN_CONFIG_DICT.get(ENV_NAME).get(DATA_TABLE_NAME)

    async def chat(self, chat_request: ChatRequest):
        answer_start_time = 0
        response_id = str(uuid.uuid4())
        chat_request.logger.debug(f"response_id={response_id}")
        response_content_list = list()
        try:
            async for chat_response in self.chat_inner(chat_request):
                if chat_response.event == EventType.START_EVENT:
                    answer_start_time = get_cur_millis()
                chat_response_data = chat_response.data
                chat_response_data.answer_start_time = answer_start_time
                chat_response.conversation_id = chat_request.conversation_id
                # 生成一个 request id 给后端，统一存储，并且和 request id 区分开
                chat_response.request_id = response_id
                chat_response_data.request_receive_time = chat_request.request_receive_time
                chat_response_data.model_version = MODEL_VERSION
                chat_response_data.answer_intent = chat_request.answer_intent
                if chat_response_data.selected_item is None:
                    # 如果没有产生新的 selected_item，就用上一次的
                    chat_response_data.selected_item = chat_request.ending_selected_item
                if chat_response.event == EventType.FINISH_EVENT:
                    # 回答完毕，把剩下的 task 都取消
                    await TaskManager.cancel_task_dict(chat_request)
                    # 加上准备阶段的大模型 token 数量
                    chat_request.logger.debug(f"流式回答消耗 token 个数={chat_response.data.total_tokens}")
                    chat_response.data.total_tokens += sum(
                        [task.call_info.token_count for task in
                         chat_request.task_dict.values()]) if chat_request.task_dict is not None else 0
                    chat_request.logger.debug(f"总消耗 token 个数={chat_response.data.total_tokens}")
                    chat_response_data.answer_finish_time = get_cur_millis()
                    if chat_request.task_dict is not None:
                        self.report_chat_cost(chat_request, chat_response)
                    response_content = "".join(response_content_list)
                    chat_response_data.text = response_content
                    # 答案溯源
                    if chat_request.trace_source_info is not None:
                        chat_response_data.doc_trace_source_list = await self._query_parse_service.trace_source(
                            chat_request, response_content)
                    if chat_request.task_dict is not None:
                        for name in chat_request.task_dict:
                            chat_response_data.call_info_dict[name] = chat_request.task_dict[name].call_info
                    self.insert_to_db(chat_request, chat_response, response_content)
                    if chat_request.chat_request_key:
                        self._redis_manager.set(chat_request.chat_request_key, response_content, ex=604800)
                    if not chat_request.debug:
                        # 前端要求最后一个 FINISH_EVENT text 为空串（为了和中国区逻辑兼容）
                        chat_response_data.text = ""
                        # 去掉 prompt call_info_dict 节省网络传输
                        chat_response_data.prompt = ""
                        chat_response_data.call_info_dict = dict()
                response_content_list.append(chat_response_data.text)
                # to SSE str, starts with "data:"
                yield encode_sse(chat_response)

        except RequestCancelledException:
            # 捕获取消异常后，生成并发送取消消息
            chat_request.logger.info(f"处理请求 {chat_request.request_id} 时被取消。")
            response_content = "".join(response_content_list)
            async for finish_event in self.send_cancel_events(chat_request, response_content, response_id,
                                                              answer_start_time):
                yield finish_event

        except ExceptionGroup as eg:
            chat_request.logger.error(f"chat 内部报错 {str(eg)}: {traceback.format_exc()}")
            # 处理来自 TaskGroup 的 ExceptionGroup
            for exc in eg.exceptions:
                if isinstance(exc, RequestCancelledException):
                    chat_request.logger.info(f"处理请求 {chat_request.request_id} 时被取消。")
                    response_content = "".join(response_content_list)
                    async for finish_event in self.send_cancel_events(chat_request, response_content, response_id,
                                                                      answer_start_time):
                        yield finish_event
                    break  # 只处理第一个取消异常

        except Exception as e:
            # 捕获其他异常并记录
            chat_request.logger.error(f"chat 内部报错 {str(e)}: {traceback.format_exc()}")
            raise

    @staticmethod
    def get_first_token_elapse(chat_request: ChatRequest, chat_response: ChatResponse):
        # 如果用户取消，answer_start_time 可能为 0，此时 first_token_elapse 为负数，需要特殊处理
        return max(chat_response.data.answer_start_time - chat_request.request_receive_time, 0)

    def report_chat_cost(self, chat_request: ChatRequest, chat_response: ChatResponse):
        # 统计除了大模型调用、知识召回外其他的耗时时间
        await_time_list = list()
        for task_name in chat_request.task_dict:
            task = chat_request.task_dict[task_name]
            if task.call_info.status == CallStatus.USED and task.call_info.await_start_time > 0 and task.call_info.await_end_time > 0:
                await_time_list.append((task.call_info.await_start_time, task.call_info.await_end_time, task_name))
        if is_empty(await_time_list):
            return

        sorted_await_time_list = sorted(await_time_list, key=lambda x: x[0])
        total_non_llm_elapse = sorted_await_time_list[0][0] - chat_request.request_receive_time
        chat_request.logger.debug(
            f"非大模型调用耗时（接受请求 -> {sorted_await_time_list[0][2]}）：{total_non_llm_elapse}")
        for i in range(len(sorted_await_time_list) - 1):
            cur_cost = sorted_await_time_list[i + 1][0] - sorted_await_time_list[i][1]
            chat_request.logger.debug(
                f"非大模型调用耗时（{sorted_await_time_list[i][2]} -> {sorted_await_time_list[i + 1][2]}）：{cur_cost}")
            total_non_llm_elapse += cur_cost
        if chat_request.call_answer_llm_time > 0:
            cur_cost = chat_request.call_answer_llm_time - sorted_await_time_list[-1][1]
            chat_request.logger.debug(
                f"非大模型调用耗时（{sorted_await_time_list[-1][2]} -> 调用大模型回答）：{cur_cost}")
            total_non_llm_elapse += cur_cost
        chat_request.logger.debug(f"非大模型调用总耗时：{get_elapse_str(total_non_llm_elapse)}")
        if total_non_llm_elapse > 100:
            chat_request.logger.warning(f"非大模型调用耗时过长：{get_elapse_str(total_non_llm_elapse)}")
        if chat_request.call_answer_llm_time > 0:
            answer_prepare_elapse = chat_request.call_answer_llm_time - chat_request.request_receive_time
            chat_request.logger.debug(f"回答准备耗时：{get_elapse_str(answer_prepare_elapse)}")
        # 上报各个 task 信息
        for task_name in chat_request.task_dict:
            task = chat_request.task_dict[task_name]
            chat_request.logger.debug(f"task_name={task_name}, call_info={task.call_info.to_dict()}")
        first_token_elapse = self.get_first_token_elapse(chat_request, chat_response)
        if first_token_elapse < 10000:
            chat_request.logger.debug(f"首 token 耗时（含如思）：{get_elapse_str(first_token_elapse)}")
        else:
            chat_request.logger.warning(f"首 token 耗时（含如思）过长：{get_elapse_str(first_token_elapse)}")
        self._timer.labels(object="first_token_elapse",
                           condition=chat_response.data.answer_type.name).observe(first_token_elapse)
        chat_response_json = json.dumps(chat_response.to_dict(), indent=2, ensure_ascii=False)
        chat_request.logger.debug(f"回答完成，回答内容：\n{chat_response_json}")

    async def chat_inner(self, chat_request: ChatRequest) -> ChatResponse:
        """子类需要实现此方法"""
        raise NotImplementedError("子类必须实现 chat_inner 方法")

    def insert_to_db(self, chat_request, finish_chat_response: ChatResponse, all_response: str):
        try:
            start_time = time.time()
            insert_record = DBRecord(
                conversation_id=chat_request.conversation_id,
                question_id=chat_request.request_id,
                add_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                question_content=chat_request.ending_message.content if hasattr(chat_request.ending_message,
                                                                                "content") else chat_request.ending_message,
                response=all_response,
                first_token_elapse=self.get_first_token_elapse(chat_request, finish_chat_response),
                request_receive_time=finish_chat_response.data.request_receive_time,
                answer_start_time=finish_chat_response.data.answer_start_time,
                answer_finish_time=finish_chat_response.data.answer_finish_time,
                total_tokens=finish_chat_response.data.total_tokens,
                answer_type=finish_chat_response.data.answer_type,
                intents=chat_request.answer_intent,
                # 这里存的是「最终」的吸顶机型，而不是一开始传入的
                selected_item_names=chat_request.item_name,
                # ToDo(hm): selected_item_names 字段其实存的是 actual_item_name_list，原始的 selected_item_names 应该另外存
                actual_item_names=",".join(finish_chat_response.data.actual_item_name_list),
                item_attributes=",".join(chat_request.second_tags),
                model_version=MODEL_VERSION,
                dt=datetime.datetime.now().strftime('%Y%m%d'),
                chat_request=to_json_str(chat_request),
                prompt=finish_chat_response.data.prompt,
                system_language=chat_request.language.value,
                input_language=chat_request.recognize_language.value if chat_request.recognize_language is not None else chat_request.language.value,
                output_language=chat_request.recognize_language.value if chat_request.recognize_language is not None else chat_request.language.value,
                area=chat_request.area.value,
                version=chat_request.version,
                response_id=finish_chat_response.request_id,
            ).to_dict()
            chat_request.logger.debug(f"写入数据库：{insert_record}")
            self._db.insert_record(self._data_table_name, insert_record)
            chat_request.logger.debug(f"insert_data: {time.time() - start_time}")
        except Exception as e:
            chat_request.logger.error(f"写入数据库发生错误 {str(e)}: {traceback.format_exc()}")

    def refuse_answer(self, refuse_type: MessageType, chat_request: ChatRequest):
        # 更新统计
        self._counter.labels(object=CHAT, condition=refuse_type.name).inc()
        # 生成并返回拒绝回答的事件
        language = chat_request.language
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language
        refusal_message = REFUSAL_MESSAGE_DICT[refuse_type][language]
        for chat_response in self.wrap_str_to_response_stream(refusal_message, answer_type=refuse_type):
            yield chat_response

    def cache_answer(self, content: str, answer_type: MessageType):
        # 更新统计
        self._counter.labels(object=CHAT, condition=answer_type).inc()
        for chat_response in self.wrap_str_to_response_stream(content, answer_type=answer_type):
            yield chat_response

    def set_task_id_and_api_key(self, chat_request: ChatRequest, task_id: str, api_key: str):
        try:
            key = f"request:{chat_request.request_id}"
            if self._redis_manager.exists(key):
                # 仅更新 task_id 字段，不改变 should_cancel
                self._redis_manager.hset(key, "task_id", task_id)
                self._redis_manager.hset(key, "api_key", api_key)
                chat_request.logger.info(
                    f"更新 task_id 为 {task_id}，api_key 为 {api_key}，request_id: {chat_request.request_id}")
            else:
                # 设置 task_id 和 should_cancel 字段
                self._redis_manager.hset(key,
                                         mapping={"task_id": task_id, "api_key": api_key, "should_cancel": "False"})
                # 设置一个过期时间（可选）
                self._redis_manager.expire(key, 3600)  # 1 小时后自动过期
                chat_request.logger.info(
                    f"设置 task_id 为 {task_id}，api_key 为 {api_key}，should_cancel 为 False，request_id: {chat_request.request_id}")
        except Exception as e:
            chat_request.logger.error(f"设置 task_id 时发生错误: {traceback.format_exc()}")

    async def generate_response_stream_mify(self, system_prompt, user_prompt, chat_request: ChatRequest, answer_type,
                                            need_first_event=True, pre_thinking_str=""):
        is_first_chunk = True
        task_id = None  # 用于存储 task_id
        timeout_duration = 10
        chat_request.call_answer_llm_time = get_cur_millis()
        # 创建一个单一的异步生成器实例
        async_stream = self._model_manager.call_llm_with_stream(system_prompt, user_prompt, chat_request)
        try:
            while True:
                try:
                    # 使用 asyncio.wait_for 为每个响应块设置超时
                    response = await asyncio.wait_for(async_stream.__anext__(), timeout=timeout_duration)
                except StopAsyncIteration:
                    # 生成器已结束
                    break
                except asyncio.TimeoutError:
                    # 超时后检查请求是否已被取消
                    if should_cancel(self._redis_manager, chat_request):
                        chat_request.logger.info(f"Timeout reached. Request canceled. task_id: {task_id}")
                        raise RequestCancelledException(request_id=chat_request.request_id)
                    # 如果请求未被取消，可以选择记录警告或采取其他措施
                    chat_request.logger.warning(f"Timeout reached but request not canceled. Continuing...")
                    raise

                llm_response_data_dict = decode_sse(response.decode("utf-8"))
                if is_empty(llm_response_data_dict) or "event" not in llm_response_data_dict:
                    continue

                cur_event_type = llm_response_data_dict["event"]
                # 捕获 task_id
                if cur_event_type == "workflow_started" and "task_id" in llm_response_data_dict["data"]:
                    task_id = llm_response_data_dict["data"]["task_id"]
                    chat_request.logger.info(f"Retrieved task_id: {task_id}")
                    self.set_task_id_and_api_key(chat_request, task_id, self._api_key_text)

                if cur_event_type == "text_chunk":
                    if is_first_chunk and need_first_event:
                        # 追加 start event
                        chat_response = ChatResponse(
                            event=EventType.START_EVENT,
                            data=ChatResponseData(answer_type=answer_type)
                        )
                        is_first_chunk = False
                        yield chat_response

                    chat_response = ChatResponse(
                        event=EventType.TEXT_CHUNK_EVENT,
                        data=ChatResponseData(
                            text=llm_response_data_dict["data"]["text"],
                            answer_type=answer_type
                        )
                    )
                    yield chat_response

                if cur_event_type == "workflow_finished":
                    response_text = pre_thinking_str + llm_response_data_dict["data"]["outputs"]["answer"]
                    response_data = ChatResponseData(text=response_text, answer_type=answer_type)
                    prompt = f"[system]{system_prompt}[user]{user_prompt}"
                    response_data.prompt = prompt
                    response_data.total_tokens = llm_response_data_dict["data"]["total_tokens"]
                    chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                    yield chat_response
                    break

        except RequestCancelledException:
            raise
        except Exception as e:
            # 记录其他异常
            chat_request.logger.error(f"Error in generate_response_stream: {str(e)}")
            raise

    async def generate_response_stream(self, system_prompt, user_prompt, chat_request: ChatRequest, answer_type,
                                       need_first_event=True, pre_thinking_str=""):
        if isinstance(self._model_manager, ModelManagerMify):
            async for chat_response in self.generate_response_stream_mify(system_prompt, user_prompt, chat_request,
                                                                          answer_type, need_first_event,
                                                                          pre_thinking_str):
                yield chat_response
            return
        else:
            async for chat_response in self.generate_response_stream_openai(system_prompt, user_prompt, chat_request,
                                                                            answer_type, need_first_event,
                                                                            pre_thinking_str):
                yield chat_response

    async def generate_response_stream_openai(self, system_prompt, user_prompt, chat_request: ChatRequest, answer_type,
                                              need_first_event=True, pre_thinking_str=""):
        is_first_chunk = True
        # 用于存储 task_id
        task_id = None
        timeout_duration = 10
        chat_request.call_answer_llm_time = get_cur_millis()
        # 创建一个单一的异步生成器实例
        async_stream = self._model_manager.call_llm_with_stream(system_prompt, user_prompt, chat_request)
        try:
            while True:
                try:
                    # 使用 asyncio.wait_for 为每个响应块设置超时
                    response_chunk = await asyncio.wait_for(async_stream.__anext__(), timeout=timeout_duration)
                except StopAsyncIteration:
                    # 生成器已结束
                    break
                except asyncio.TimeoutError:
                    # 超时后检查请求是否已被取消
                    if should_cancel(self._redis_manager, chat_request):
                        chat_request.logger.info(f"Timeout reached. Request canceled. task_id: {task_id}")
                        raise RequestCancelledException(request_id=chat_request.request_id)
                    # 如果请求未被取消，可以选择记录警告或采取其他措施
                    chat_request.logger.warning(f"Timeout reached but request not canceled. Continuing...")
                    raise

                if is_first_chunk and need_first_event:
                    # 追加 start event
                    chat_response = ChatResponse(
                        event=EventType.START_EVENT,
                        data=ChatResponseData(answer_type=answer_type)
                    )
                    is_first_chunk = False
                    yield chat_response

                if response_chunk.event == EventType.TEXT_CHUNK_EVENT:
                    chat_response = ChatResponse(
                        event=EventType.TEXT_CHUNK_EVENT,
                        data=ChatResponseData(
                            text=response_chunk.text,
                            answer_type=answer_type
                        )
                    )
                    yield chat_response
                    continue

                if response_chunk.event == EventType.FINISH_EVENT:
                    prompt = f"[system]{system_prompt}[user]{user_prompt}"
                    response_data = ChatResponseData(answer_type=answer_type, prompt=prompt,
                                                     total_tokens=response_chunk.token_num)
                    chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                    yield chat_response
                    # break

        except RequestCancelledException:
            raise
        except Exception as e:
            # 记录其他异常
            chat_request.logger.error(f"Error in generate_response_stream: {str(e)}")
            raise

    @staticmethod
    def wrap_str_to_response_stream(content, answer_type, selected_item=None, item_list=None, need_finish_event=True):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type, selected_item=selected_item, item_list=item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, selected_item=selected_item,
                                  item_list=item_list)
        )
        yield chat_response

        if need_finish_event:
            chat_response = ChatResponse(
                event=EventType.FINISH_EVENT,
                data=ChatResponseData(text=content, answer_type=answer_type, selected_item=selected_item,
                                      item_list=item_list)
            )
            yield chat_response

    def get_item_by_name(self, item_name) -> Optional[Item]:
        if is_empty(item_name):
            return None

        return self._name_item_dict.get(item_name, None)

    def get_item_name_not_in_candidates(self, extracted_item_names):
        normalized_candidates = set(self._normalized_item_name_list)
        # 保留未在候选列表中的原始名称
        not_in_candidates = [
            name for name in extracted_item_names
            if normalize_item_name(name) not in normalized_candidates
        ]
        return not_in_candidates

    async def send_cancel_events(self, chat_request, response_content, response_id, answer_start_time):
        language = chat_request.get_final_language()
        response_content += REFUSAL_MESSAGE_DICT[MessageType.CANCELLED][language]
        answer_start_time = get_cur_millis() if answer_start_time == 0 else answer_start_time
        answer_finish_time = get_cur_millis()
        # 生成并发送包含取消消息的 FINISH_EVENT
        final_data_cancel = ChatResponseData(
            is_cancelled=True,
            answer_type=MessageType.CANCELLED,
            text=REFUSAL_MESSAGE_DICT[MessageType.CANCELLED][language],
            request_receive_time=chat_request.request_receive_time,
            answer_start_time=answer_start_time,
            answer_finish_time=answer_finish_time,
            model_version=MODEL_VERSION,
            selected_item=chat_request.ending_selected_item
        )

        final_chat_response_cancel = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=final_data_cancel,
            conversation_id=chat_request.conversation_id,
            request_id=response_id,
        )
        self.insert_to_db(chat_request, final_chat_response_cancel, response_content)
        if chat_request.chat_request_key:
            self._redis_manager.set(chat_request.chat_request_key, response_content, ex=604800)
        yield encode_sse(final_chat_response_cancel)

        # 生成并发送包含空字符串的 FINISH_EVENT
        final_data_empty = ChatResponseData(
            answer_type=MessageType.TEXT,
            text="",
            request_receive_time=chat_request.request_receive_time,
            answer_start_time=answer_start_time,
            answer_finish_time=answer_finish_time,
            model_version=MODEL_VERSION,
            selected_item=chat_request.ending_selected_item
        )

        final_chat_response_empty = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=final_data_empty,
            conversation_id=chat_request.conversation_id,
            request_id=response_id,
        )

        yield encode_sse(final_chat_response_empty)

    @staticmethod
    def get_pre_thinking_str(chat_request, second_tags_map_dict):
        language = chat_request.get_final_language()
        # item_name 不能为空
        if is_empty(chat_request.item_name):
            # 好的，让我来帮您解答。
            return random.choice(PRE_MESSAGE_DICT[PreThinkingMessageType.NO_ITEM_NAME])[language]

        if is_empty(second_tags_map_dict):
            # 好的，让我帮你回答你关于 item_name 的问题
            return random.choice(PRE_MESSAGE_DICT[PreThinkingMessageType.NO_SECOND_TAG])[language].format(
                chat_request.item_name)

        tag_str = ', '.join(second_tags_map_dict.values())
        # 好的，让我来帮您解答关于 item_name 的 x 方面的问题
        return random.choice(PRE_MESSAGE_DICT[PreThinkingMessageType.DEFAULT])[language].format(
            chat_request.item_name, tag_str)

    @staticmethod
    def get_pre_thinking_for_item_compare(chat_request, first_item, second_item):
        """
        双机对比如思
        """
        # 好的，让我来帮您解答关于 item_name 的 x 方面的问题
        return random.choice(PRE_MESSAGE_DICT[PreThinkingMessageType.TWO_PHONE_COMPARE])[
            chat_request.get_final_language()].format(
            first_item, second_item)


def encode_sse(chat_response: ChatResponse):
    data = chat_response.to_dict()
    return f"data: {json.dumps(data)}\n\n"


async def get_suggest_queries(request, competitor_map, item_id_name_map, model_manager, sample_size, logger):
    suggest_queries = []
    if request.answer_intent is not None and (
            request.answer_intent in INTENT_CATEGORY_DICT[FREE_QA_CAT] or
            request.answer_intent in INTENT_CATEGORY_DICT[REJECT_QA_CAT]):
        return suggest_queries

    chat_history, item_name, competitors = "空", "无", "无"
    if request.item_id is not None:
        item_name = item_id_name_map.get(request.item_id, "无")
    if request.chat_history is not None:
        str_message_list = get_str_message_list(request.chat_history, request.language)
        if not_empty(str_message_list):
            chat_history = "\n".join(str_message_list)

        if item_name == "无":
            ending_selected_item = request.chat_history[-1].messages[-1].selected_item
            if ending_selected_item is not None:
                item_name = ending_selected_item.item_name
    if item_name != "无":
        competitors = list(competitor_map.get(item_name, {}).get(request.language.get_chinese_name(), []))
        competitors = ', '.join(competitors)

    system_prompt = SUGGEST_QUERIES_SYSTEM_PROMPT.format(language=request.language.get_chinese_name())
    user_prompt = SUGGEST_QUERIES_USER_PROMPT.format(chat_history=chat_history, item_name=item_name,
                                                     competitors=competitors)
    is_success, response = await model_manager.call_llm_with_json(system_prompt, user_prompt)
    logger.info(f'suggest_queries response: {response}')
    if not is_success:
        return suggest_queries
    current_item_questions = response.get(SUGGEST_QUERIES_KEY, {}).get(QUERIES_ABOUT_CURRENT_ITEM, [])
    competitor_comparison_questions = response.get(SUGGEST_QUERIES_KEY, {}).get(COMPARISON_WITH_COMPETITORS, [])

    all_questions = current_item_questions + competitor_comparison_questions
    suggest_queries = random.sample(all_questions, min(sample_size, len(all_questions)))

    for question in suggest_queries:
        assert isinstance(question, str), f"suggest_queries {question} is not a string"
    return suggest_queries
