from typing import Optional
from pydantic import BaseModel

from core.enum.content_type import ContentType
from core.enum.doc_type import DocType


class DocTraceSource(BaseModel):
    item_id: Optional[str]
    item_name: str
    doc_type: DocType
    # 展示标题
    title: str
    # 内容类型：1-HTML 2-URL 3-KEY（用于定位的信息）
    content_type: ContentType
    # 展示内容
    content: Optional[str] = None
    # 原始内容
    raw_content: Optional[str] = None
