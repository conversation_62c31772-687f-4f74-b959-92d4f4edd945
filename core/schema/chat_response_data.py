from typing import Optional, List

from pydantic import BaseModel

from core.enum.message_type import MessageType
from core.schema.doc_trace_source import DocTraceSource
from core.schema.item import Item


class ChatResponseData(BaseModel):
    answer_type: Optional[MessageType] = MessageType.TEXT
    model_version: str = ""
    answer_start_time: int = 0
    request_receive_time: int = 0
    is_cancelled: bool = False
    prompt: Optional[str] = None
    text: Optional[str] = ""
    selected_item: Optional[Item] = None
    item_list: Optional[List[Item]] = None
    answer_finish_time: Optional[int] = 0
    total_tokens: Optional[int] = 0
    time_cost: Optional[str] = ""
    actual_item_name_list: Optional[List[str]] = list()
    call_info_dict: Optional[dict] = dict()
    answer_intent: Optional[str] = ""
    # 答案溯源字段
    doc_trace_source_list: Optional[List[DocTraceSource]] = list()

    def to_dict(self):
        data = self.model_dump()
        # 确保 doc_trace_source_list 中的枚举类型被正确序列化
        if data.get("doc_trace_source_list"):
            for source in data["doc_trace_source_list"]:
                if isinstance(source, dict):
                    if "doc_type" in source and hasattr(source["doc_type"], "value"):
                        source["doc_type"] = source["doc_type"].value
                    if "content_type" in source and hasattr(source["content_type"], "value"):
                        source["content_type"] = source["content_type"].value
        return {k: v for k, v in data.items() if v is not None}

    @classmethod
    def from_dict(cls, data_dict: dict):
        # Handle answer_type conversion from int to MessageType enum if needed
        if "answer_type" in data_dict and isinstance(data_dict["answer_type"], int):
            data_dict["answer_type"] = MessageType(data_dict["answer_type"])

        # Handle selected_item conversion from dict to Item object
        if "selected_item" in data_dict and data_dict["selected_item"] is not None:
            if isinstance(data_dict["selected_item"], dict):
                data_dict["selected_item"] = Item(**data_dict["selected_item"])

        # Handle item_list conversion from list of dicts to list of Item objects
        if "item_list" in data_dict and data_dict["item_list"] is not None:
            if isinstance(data_dict["item_list"], list):
                data_dict["item_list"] = [Item(**item) if isinstance(item, dict) else item
                                          for item in data_dict["item_list"]]

        # Handle doc_trace_source_list conversion from list of dicts to list of DocTraceSource objects
        if "doc_trace_source_list" in data_dict and data_dict["doc_trace_source_list"] is not None:
            if isinstance(data_dict["doc_trace_source_list"], list):
                data_dict["doc_trace_source_list"] = [DocTraceSource(**source) if isinstance(source, dict) else source
                                                     for source in data_dict["doc_trace_source_list"]]

        return cls(**data_dict)
