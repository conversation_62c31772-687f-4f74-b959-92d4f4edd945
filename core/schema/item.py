from typing import Optional, List, Dict

from pydantic import BaseModel


class Item(BaseModel):
    item_id: str
    item_name: str
    category_id: Optional[str] = None
    category_name: Optional[str] = None
    first_category: Optional[str] = None
    second_category: Optional[str] = None
    is_xiaomi: Optional[bool] = False
    # 聚合所有 faq
    faq_url_list: List[str] = list()
    # name to sale tool url list
    sale_tool_url_dict: Dict[str, List[str]] = dict()

    def __repr__(self):
        return self.item_name

    def __str__(self):
        return self.item_name
